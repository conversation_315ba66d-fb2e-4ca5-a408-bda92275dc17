import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { Suspense } from 'react';
import ConsolidationClient from '@/components/exercises/ConsolidationClient';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Database as DatabaseIcon } from 'lucide-react';
import Link from 'next/link';

// Server-side data fetching for consolidation
async function fetchConsolidationData() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  try {
    // Get all exercises with their related data for analysis
    const { data: exercises, error: exerciseError } = await supabase
      .from('exercise_movements')
      .select('*')
      .order('exercise_name');

    if (exerciseError) {
      console.error('Error fetching exercises:', exerciseError);
      throw exerciseError;
    }

    if (!exercises || exercises.length === 0) {
      return {
        exercises: [],
        categories: [],
        images: [],
        videos: [],
        recordCounts: new Map(),
        totalExercises: 0
      };
    }

    const exerciseIds = exercises.map(ex => ex.id);

    // Get all related data
    const [categoriesResponse, imagesResponse, videosResponse] = await Promise.all([
      supabase
        .from('exercise_categories')
        .select('*')
        .in('exercise_id', exerciseIds),
      
      supabase
        .from('exercise_images')
        .select('*')
        .in('exercise_id', exerciseIds),
      
      supabase
        .from('exercise_videos')
        .select('*')
        .in('exercise_id', exerciseIds)
    ]);

    // Get record counts for each exercise
    const { data: recordData, error: recordError } = await supabase
      .from('exercise_records')
      .select('exercise_id')
      .in('exercise_id', exerciseIds);

    if (recordError) {
      console.error('Error fetching record counts:', recordError);
    }

    // Count records per exercise
    const recordCounts = new Map<string, number>();
    (recordData || []).forEach(record => {
      const count = recordCounts.get(record.exercise_id) || 0;
      recordCounts.set(record.exercise_id, count + 1);
    });

    return {
      exercises,
      categories: categoriesResponse.data || [],
      images: imagesResponse.data || [],
      videos: videosResponse.data || [],
      recordCounts,
      totalExercises: exercises.length
    };

  } catch (error) {
    console.error('Error in fetchConsolidationData:', error);
    return {
      exercises: [],
      categories: [],
      images: [],
      videos: [],
      recordCounts: new Map(),
      totalExercises: 0
    };
  }
}

// Loading component
function ConsolidationLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-10">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="h-32 bg-gray-200 rounded mb-6"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default async function ExerciseConsolidationPage() {
  const data = await fetchConsolidationData();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-10">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link 
              href="/exercises" 
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Exercises
            </Link>
          </div>
          
          <div className="flex items-center gap-3 mb-2">
            <DatabaseIcon className="h-6 w-6 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Exercise Consolidation</h1>
          </div>
          
          <p className="text-gray-600">
            Merge duplicate or similar exercises to improve data quality and user experience.
          </p>
        </div>

        {/* Warning Alert */}
        <Alert className="mb-6 border-orange-200 bg-orange-50">
          <AlertDescription className="text-orange-800">
            <strong>Important:</strong> Exercise consolidation will permanently merge exercise records. 
            Ensure you have a recent backup before proceeding. This action affects user data and cannot be easily undone.
          </AlertDescription>
        </Alert>

        {/* Stats Card */}
        <Card className="p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{data.totalExercises}</div>
              <div className="text-sm text-gray-600">Total Exercises</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Array.from(data.recordCounts.values()).reduce((sum, count) => sum + count, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{data.categories.length}</div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {data.images.length + data.videos.length}
              </div>
              <div className="text-sm text-gray-600">Media Files</div>
            </div>
          </div>
        </Card>

        {/* Main Consolidation Interface */}
        <Suspense fallback={<ConsolidationLoading />}>
          <ConsolidationClient 
            exercises={data.exercises}
            categories={data.categories}
            images={data.images}
            videos={data.videos}
            recordCounts={data.recordCounts}
          />
        </Suspense>
      </div>
    </div>
  );
}