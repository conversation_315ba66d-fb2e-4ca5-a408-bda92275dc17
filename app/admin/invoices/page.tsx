// app/admin/invoices/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { Plus, Eye, CheckCircle, AlertCircle } from 'lucide-react';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function InvoicesPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Get invoices
  const { data: invoices, error } = await supabase
    .from('invoices')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching invoices:', error);
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('el-GR');
  };

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Invoices</h1>

        <Link href="/admin/mydata/test">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Test Invoice
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          {invoices && invoices.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Series-Number</TableHead>
                  <TableHead>Issue Date</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>MARK</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      {invoice.invoice_series}-{invoice.invoice_number}
                    </TableCell>
                    <TableCell>{formatDate(invoice.issue_date)}</TableCell>
                    <TableCell>{invoice.client_name}</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(invoice.total_gross)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {invoice.status === 'submitted' ? (
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        ) : invoice.status === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
                        ) : (
                          <></>
                        )}
                        <span className={
                          invoice.status === 'submitted' ? 'text-green-600' :
                          invoice.status === 'error' ? 'text-red-600' :
                          'text-gray-600'
                        }>
                          {invoice.status}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {invoice.mark ? (
                        <span className="text-xs font-mono">
                          {invoice.mark.substring(0, 10)}...
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Link href={`/admin/invoices/${invoice.id}`}>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No invoices found. Create a test invoice to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}