// app/admin/mydata/settings/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import MyDataSettingsForm from '@/components/mydata/MyDataSettingsForm';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

export default async function MyDataSettingsPage() {
  const supabase = createServerComponentClient<Database>({ cookies });
  
  // Ensure user is authenticated and is admin
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>Please log in to access this page.</p>
      </div>
    );
  }
  
  // Check if user has admin role
  const { data: userRoles } = await supabase
    .from('user_roles')
    .select('role_id')
    .eq('auth_user_id', session.user.id);
  
  const isAdmin = userRoles?.some(ur => [1, 2].includes(ur.role_id)); // Assuming 1 or 2 are admin roles
  
  if (!isAdmin) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }
  
  // Get company settings
  const { data: companySettings } = await supabase
    .from('company_settings')
    .select('*')
    .single();
  
  // Check if environment variables are set
  const environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';
  
  const hasCredentials = environment === 'production'
    ? !!process.env.MYDATA_USERNAME_PROD && !!process.env.MYDATA_SUBSCRIPTION_KEY_PROD
    : !!process.env.MYDATA_USERNAME_DEV && !!process.env.MYDATA_SUBSCRIPTION_KEY_DEV;
  
  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">myDATA Settings</h1>
      
      <MyDataSettingsForm 
        companySettings={companySettings || undefined}
        environmentInfo={{
          environment,
          hasCredentials
        }}
      />
    </div>
  );
}