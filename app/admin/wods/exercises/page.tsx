import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import ExercisesClient from '@/components/exercises/ExercisesClient';

// page.tsx
// page.tsx
export default async function ExercisesPage() {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Fetch the total count first
  const { count, error: countError } = await supabase
    .from('exercise_movements')
    .select('*', { count: 'exact', head: true });
  
  if (countError) {
    console.error('Error fetching count:', countError);
  }

  // Then fetch the first page of data
  const { data: initialExercises, error } = await supabase
    .from('exercise_movements')
    .select('*')
    .order('exercise_name')
    .range(0, 3000); 

  console.log(`Fetched ${initialExercises?.length || 0} exercises of ${count} total`);

  if (error) {
    console.error('Error fetching exercises:', error);
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ExercisesClient 
        initialExercises={initialExercises || []} 
        totalExercises={count || 0} 
      />
    </div>
  );
}