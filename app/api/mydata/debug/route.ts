// app/api/mydata/debug/route.ts
import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function POST(request: Request) {
  try {
    const { xml } = await request.json();
    
    // Initialize myDATA service
    const myDataService = new MyDataService('development'); 
    
    // Attempt to send with full error details
    try {
      const response = await myDataService.sendInvoice(xml);
      return NextResponse.json({
        success: true,
        responseType: typeof response,
        responseLength: response.length,
        rawResponse: response
      });
    } catch (error: any) {
      return NextResponse.json({
        success: false,
        errorMessage: error.message,
        errorType: error.constructor.name,
        responseData: error.response?.data,
        responseStatus: error.response?.status,
        requestHeaders: error.config?.headers,
        stack: error.stack
      });
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      setup: 'Failed',
      errorMessage: error.message,
      errorStack: error.stack
    });
  }
}