// app/api/mydata/generate-pdf/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateReactInvoicePdf } from '@/lib/mydata/reactPdfGenerator';
import type { Database } from '@/types/supabase';

// Import or redefine the CompanySettings type to match exactly what pdfGenerator expects
interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

export async function POST(request: NextRequest) {
  try {
    // Parse request
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    const { invoiceId } = body;

    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    console.log('Processing PDF generation for invoice ID:', invoiceId);

    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get company settings
    const { data: companySettingsData, error: settingsError } = await supabase
      .from('company_settings')
      .select('*')
      .single();

    if (settingsError || !companySettingsData) {
      console.error('Failed to load company settings:', settingsError);
      return NextResponse.json(
        { error: 'Failed to load company settings' },
        { status: 500 }
      );
    }

    // Get invoice data
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      console.error('Failed to load invoice:', invoiceError);
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get invoice lines
    const { data: lines, error: linesError } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('line_number');

    if (linesError || !lines || lines.length === 0) {
      console.error('Failed to load invoice lines:', linesError);
      return NextResponse.json(
        { error: 'Invoice lines not found' },
        { status: 404 }
      );
    }

    // Get payment methods
    const { data: paymentMethods, error: paymentsError } = await supabase
      .from('invoice_payment_methods')
      .select('*')
      .eq('invoice_id', invoiceId);

    if (paymentsError) {
      console.error('Failed to load payment methods:', paymentsError);
      // Continue without payment methods if not found
    }

    // Generate PDF
    try {
      console.log('Preparing data for PDF generation');

      // Validate invoice data
      if (!invoice) {
        throw new Error('Invoice data is missing or invalid');
      }

      // Validate company settings
      if (!companySettingsData || !companySettingsData.companyName || !companySettingsData.vatNumber) {
        throw new Error('Company settings are missing or invalid');
      }

      // Validate invoice lines
      if (!lines || lines.length === 0) {
        throw new Error('Invoice lines are missing or empty');
      }

      // Log data for debugging
      console.log('Invoice data:', {
        id: invoice.id,
        series: invoice.invoice_series,
        number: invoice.invoice_number,
        lineCount: lines.length
      });

      // Adapt the invoice data to match the expected InvoiceData type
      const adaptedInvoice = {
        ...invoice,
        client_country: invoice.client_country || 'GR', // Default to Greece if null
        currency: invoice.currency || 'EUR', // Default to EUR if null
        created_at: invoice.created_at || new Date().toISOString(),
        updated_at: invoice.updated_at || new Date().toISOString(),
      };

      // Adapt the company settings to match the expected CompanySettings interface
      // Important: We need to map only the fields that exist in the interface
      // and provide default values for required fields that might be null
      const adaptedCompanySettings: CompanySettings = {
        id: companySettingsData.id,
        companyName: companySettingsData.companyName,
        vatNumber: companySettingsData.vatNumber,
        country: companySettingsData.country || 'GR',
        branch: companySettingsData.branch || '0',
        // Optional fields - handle properly
        address: companySettingsData.address || undefined,
        city: companySettingsData.city || undefined,
        postalCode: companySettingsData.postalCode || undefined,
        // Required fields that might be null in the database - provide defaults
        defaultClassificationType: companySettingsData.defaultClassificationType || 'E3_561',
        defaultClassificationCategory: companySettingsData.defaultClassificationCategory || 'category2_1',
      };

      console.log('Calling PDF generator');
      const pdfBuffer = await generateReactInvoicePdf(
        adaptedInvoice,
        lines,
        paymentMethods || [],
        adaptedCompanySettings
      );

      // Log PDF generation in API logs
      await supabase
        .from('api_logs')
        .insert({
          invoice_id: invoiceId,
          request_type: 'GeneratePDF',
          request_body: `Invoice ID: ${invoiceId}`,
          response_body: 'PDF generated successfully',
          status: 'success'
        });

      // Check if pdfBuffer is valid
      if (!pdfBuffer) {
        throw new Error('Failed to generate PDF: Buffer is empty');
      }

      // Return PDF as response
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="invoice_${invoice.invoice_series}-${invoice.invoice_number}.pdf"`,
          'Content-Length': String(pdfBuffer.length)
        }
      });
    } catch (error) {
      console.error('Error generating PDF:', error);

      // Log error in API logs
      await supabase
        .from('api_logs')
        .insert({
          invoice_id: invoiceId,
          request_type: 'GeneratePDF',
          request_body: `Invoice ID: ${invoiceId}`,
          response_body: error instanceof Error ? error.message : 'Unknown error',
          status: 'error',
          error_message: error instanceof Error ? error.message : 'Unknown error'
        });

      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'PDF generation failed' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error in PDF generation API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unexpected error' },
      { status: 500 }
    );
  }
}