// app/api/mydata/setup-database/route.ts
import { NextResponse } from 'next/server';
import { setupMyDataTables } from '@/lib/mydata/setupDatabase';

export async function POST() {
  try {
    const result = await setupMyDataTables();
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error setting up database:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    );
  }
}