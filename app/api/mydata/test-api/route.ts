import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function GET() {
  try {
    // Create a service instance
    const myDataService = new MyDataService('development');
    
    // Create a minimal test XML
    const testXml = `<?xml version="1.0" encoding="UTF-8"?>
<InvoicesDoc xmlns="http://www.aade.gr/myDATA/invoice/v1.0">
  <invoice>
    <issuer>
      <vatNumber>123456789</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </issuer>
    <counterpart>
      <vatNumber>987654321</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </counterpart>
    <invoiceHeader>
      <series>TEST</series>
      <aa>1</aa>
      <issueDate>2023-05-19</issueDate>
      <invoiceType>11.1</invoiceType>
      <currency>EUR</currency>
    </invoiceHeader>
    <invoiceDetails>
      <lineNumber>1</lineNumber>
      <netValue>100.00</netValue>
      <vatCategory>1</vatCategory>
      <vatAmount>24.00</vatAmount>
      <itemDescr>Test Item</itemDescr>
      <quantity>1.00</quantity>
      <measurementUnit>1</measurementUnit>
    </invoiceDetails>
    <invoiceSummary>
      <totalNetValue>100.00</totalNetValue>
      <totalVatAmount>24.00</totalVatAmount>
      <totalWithheldAmount>0.00</totalWithheldAmount>
      <totalFeesAmount>0.00</totalFeesAmount>
      <totalStampDutyAmount>0.00</totalStampDutyAmount>
      <totalOtherTaxesAmount>0.00</totalOtherTaxesAmount>
      <totalDeductionsAmount>0.00</totalDeductionsAmount>
      <totalGrossValue>124.00</totalGrossValue>
    </invoiceSummary>
  </invoice>
</InvoicesDoc>`;

    // Try to send the XML
    try {
      const response = await myDataService.sendInvoice(testXml);
      return NextResponse.json({
        success: true,
        responseType: typeof response,
        responseLength: response.length,
        responsePreview: response.substring(0, 500)
      });
    } catch (error: any) {
      return NextResponse.json({
        success: false,
        errorMessage: error.message,
        errorType: error.constructor.name,
        responseData: error.response?.data,
        responseStatus: error.response?.status
      });
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      stage: 'setup',
      errorMessage: error.message,
      errorType: error.constructor.name
    });
  }
}