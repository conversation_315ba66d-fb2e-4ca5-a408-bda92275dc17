// app/api/mydata/test-query/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get the invoice ID from the URL query parameters
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('id');
    
    // Check if invoice ID is provided
    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }
    
    // Initialize Supabase client with service role key
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    console.log('Testing database queries for invoice ID:', invoiceId);
    
    // Get invoice data
    const invoiceResponse = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();
    
    // Get invoice lines
    const linesResponse = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId);
    
    // Get payment methods
    const paymentsResponse = await supabase
      .from('invoice_payment_methods')
      .select('*')
      .eq('invoice_id', invoiceId);
    
    // Get company settings
    const settingsResponse = await supabase
      .from('company_settings')
      .select('*')
      .single();
    
    // Compile all results into a response
    return NextResponse.json({
      invoiceId,
      
      // Invoice data
      invoice: {
        data: invoiceResponse.data,
        error: invoiceResponse.error,
        found: !!invoiceResponse.data
      },
      
      // Invoice lines
      lines: {
        data: linesResponse.data,
        error: linesResponse.error,
        count: linesResponse.data?.length || 0,
        found: (linesResponse.data?.length || 0) > 0
      },
      
      // Payment methods
      payments: {
        data: paymentsResponse.data,
        error: paymentsResponse.error,
        count: paymentsResponse.data?.length || 0,
        found: (paymentsResponse.data?.length || 0) > 0
      },
      
      // Company settings
      settings: {
        data: settingsResponse.data ? {
          ...settingsResponse.data,
          // Redact any sensitive information
          vatNumber: settingsResponse.data.vatNumber ? '****' + settingsResponse.data.vatNumber.substring(4) : null
        } : null,
        error: settingsResponse.error,
        found: !!settingsResponse.data
      },
      
      // Environment information
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL
      }
    });
  } catch (error) {
    console.error('Error in test-query route:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

// Also add a simple POST endpoint to test if the same invoice ID works with POST
export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();
    const { invoiceId } = body;
    
    // Check if invoice ID is provided
    if (!invoiceId) {
      return NextResponse.json({ error: 'Invoice ID is required in request body' }, { status: 400 });
    }
    
    // Initialize Supabase client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    console.log('Testing POST request with invoice ID:', invoiceId);
    
    // Get invoice lines
    const { data: lines, error: linesError } = await supabase
      .from('invoice_lines')
      .select('*')
      .eq('invoice_id', invoiceId);
    
    return NextResponse.json({
      invoiceId,
      lines: {
        data: lines,
        error: linesError,
        count: lines?.length || 0,
        found: (lines?.length || 0) > 0
      },
      requestMethod: 'POST'
    });
  } catch (error) {
    console.error('Error in POST test-query route:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}