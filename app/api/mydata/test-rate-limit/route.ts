// app/api/mydata/test-rate-limit/route.ts
import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function GET() {
  try {
    // Create a service instance
    const myDataService = new MyDataService('development');
    
    // Create a minimal XML for testing
    const testXml = `<?xml version="1.0" encoding="UTF-8"?>
<InvoicesDoc xmlns="http://www.aade.gr/myDATA/invoice/v1.0" 
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
             xmlns:icls="https://www.aade.gr/myDATA/incomeClassificaton/v1.0" 
             xmlns:ecls="https://www.aade.gr/myDATA/expensesClassificaton/v1.0">
  <invoice>
    <issuer>
      <vatNumber>802275202</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </issuer>
    <invoiceHeader>
      <series>TEST</series>
      <aa>10001</aa>
      <issueDate>2025-05-21</issueDate>
      <invoiceType>11.1</invoiceType>
      <currency>EUR</currency>
    </invoiceHeader>
    <paymentMethods>
      <paymentMethodDetails>
        <type>4</type>
        <amount>124.00</amount>
      </paymentMethodDetails>
    </paymentMethods>
    <invoiceDetails>
      <lineNumber>1</lineNumber>
      <netValue>100.00</netValue>
      <vatCategory>1</vatCategory>
      <vatAmount>24.00</vatAmount>
      <lineComments>Test product</lineComments>
      <incomeClassification>
        <icls:classificationType>E3_561_007</icls:classificationType>
        <icls:classificationCategory>category1_3</icls:classificationCategory>
        <icls:amount>100.00</icls:amount>
      </incomeClassification>
    </invoiceDetails>
    <invoiceSummary>
      <totalNetValue>100.00</totalNetValue>
      <totalVatAmount>24.00</totalVatAmount>
      <totalWithheldAmount>0.00</totalWithheldAmount>
      <totalFeesAmount>0.00</totalFeesAmount>
      <totalStampDutyAmount>0.00</totalStampDutyAmount>
      <totalOtherTaxesAmount>0.00</totalOtherTaxesAmount>
      <totalDeductionsAmount>0.00</totalDeductionsAmount>
      <totalGrossValue>124.00</totalGrossValue>
      <incomeClassification>
        <icls:classificationType>E3_561_007</icls:classificationType>
        <icls:classificationCategory>category1_3</icls:classificationCategory>
        <icls:amount>100.00</icls:amount>
      </incomeClassification>
    </invoiceSummary>
  </invoice>
</InvoicesDoc>`;
    
    // Test with automatic retry for rate limiting
    try {
      const response = await myDataService.sendInvoice(testXml);
      return NextResponse.json({
        success: true,
        responseData: response
      });
    } catch (error: any) {
      return NextResponse.json({
        success: false,
        errorMessage: error.message,
        errorType: error.constructor.name,
        responseData: error.response?.data,
        responseStatus: error.response?.status,
        responseHeaders: error.response?.headers
      }, { status: 500 });
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      message: 'Failed to test rate limit handling',
      error: error.message
    }, { status: 500 });
  }
}