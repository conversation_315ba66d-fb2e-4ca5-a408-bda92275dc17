// app/api/mydata/test-xml-final/route.ts
import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function GET(request: Request) {
  try {
    // Get company VAT number for testing
    const { searchParams } = new URL(request.url);
    const debugMode = searchParams.has('debug');
    
    // Create corrected XML
    const xml = createCorrectedXml();
    
    // If debug mode, just return the XML
    if (debugMode) {
      return new NextResponse(xml, {
        status: 200,
        headers: {
          'Content-Type': 'text/xml',
        },
      });
    }
    
    // Initialize myDATA service
    const myDataService = new MyDataService('development');
    
    try {
      // Send XML to myDATA API
      console.log('Sending XML to myDATA API...');
      
      const response = await myDataService.sendInvoice(xml);
      
      // Return the response in a structured format
      return NextResponse.json({
        success: true,
        xmlSent: xml,
        responseText: response,
        responseLength: response.length
      });
    } catch (error: any) {
      // Return detailed error info
      return NextResponse.json({
        success: false,
        xmlSent: xml,
        error: {
          message: error.message,
          response: error.response?.data
        }
      }, { status: 500 });
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Create corrected XML based on validation errors
function createCorrectedXml(): string {
  // Use your actual company VAT from settings, not a test value
  const companyVat = "*********"; // Replace with your actual VAT
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<InvoicesDoc xmlns="http://www.aade.gr/myDATA/invoice/v1.0" 
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
             xmlns:icls="https://www.aade.gr/myDATA/incomeClassificaton/v1.0" 
             xmlns:ecls="https://www.aade.gr/myDATA/expensesClassificaton/v1.0">
  <invoice>
    <issuer>
      <vatNumber>${companyVat}</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </issuer>
    <invoiceHeader>
      <series>TEST</series>
      <aa>101</aa>
      <issueDate>2025-05-21</issueDate>
      <invoiceType>11.1</invoiceType>
      <currency>EUR</currency>
    </invoiceHeader>
    <paymentMethods>
      <paymentMethodDetails>
        <type>4</type>
        <amount>124.00</amount>
      </paymentMethodDetails>
    </paymentMethods>
    <invoiceDetails>
      <lineNumber>1</lineNumber>
      <netValue>100.00</netValue>
      <vatCategory>1</vatCategory>
      <vatAmount>24.00</vatAmount>
      <lineComments>Test product</lineComments>
      <incomeClassification>
        <icls:classificationType>E3_561_007</icls:classificationType>
        <icls:classificationCategory>category1_3</icls:classificationCategory>
        <icls:amount>100.00</icls:amount>
      </incomeClassification>
    </invoiceDetails>
    <invoiceSummary>
      <totalNetValue>100.00</totalNetValue>
      <totalVatAmount>24.00</totalVatAmount>
      <totalWithheldAmount>0.00</totalWithheldAmount>
      <totalFeesAmount>0.00</totalFeesAmount>
      <totalStampDutyAmount>0.00</totalStampDutyAmount>
      <totalOtherTaxesAmount>0.00</totalOtherTaxesAmount>
      <totalDeductionsAmount>0.00</totalDeductionsAmount>
      <totalGrossValue>124.00</totalGrossValue>
      <incomeClassification>
        <icls:classificationType>E3_561_007</icls:classificationType>
        <icls:classificationCategory>category1_3</icls:classificationCategory>
        <icls:amount>100.00</icls:amount>
      </incomeClassification>
    </invoiceSummary>
  </invoice>
</InvoicesDoc>`;
}