// app/api/mydata/test-xml-schema/route.ts
import { NextResponse } from 'next/server';
import { MyDataService } from '@/lib/mydata/service';

export async function GET(request: Request) {
  try {
    // Get parameters from URL
    const { searchParams } = new URL(request.url);
    const debug = searchParams.has('debug');
    
    // Create XML strictly according to the AADE schema
    const xml = createSchemaBasedXml();
    
    // If debug mode is enabled, just return the XML
    if (debug) {
      return new NextResponse(xml, {
        status: 200,
        headers: {
          'Content-Type': 'text/xml',
        },
      });
    }
    
    // Initialize myDATA service
    const myDataService = new MyDataService('development');
    
    try {
      // Send XML to myDATA API
      console.log('Sending XML to myDATA API...');
      console.log('XML length:', xml.length);
      
      const response = await myDataService.sendInvoice(xml);
      
      // Return the response in a structured format
      return NextResponse.json({
        success: true,
        xmlSent: xml,
        responseText: response,
        responseLength: response.length
      });
    } catch (error: any) {
      // Return detailed error info
      return NextResponse.json({
        success: false,
        xmlSent: xml,
        error: {
          message: error.message,
          response: error.response?.data
        }
      }, { status: 500 });
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Create XML strictly following the schema from InvoicesDoc-v0.6.xml
function createSchemaBasedXml(): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<InvoicesDoc xmlns="http://www.aade.gr/myDATA/invoice/v1.0">
  <invoice>
    <issuer>
      <vatNumber>123456789</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </issuer>
    <counterpart>
      <vatNumber>987654321</vatNumber>
      <country>GR</country>
      <branch>0</branch>
    </counterpart>
    <invoiceHeader>
      <series>TEST</series>
      <aa>101</aa>
      <issueDate>2025-05-21</issueDate>
      <invoiceType>11.2</invoiceType>
      <currency>EUR</currency>
    </invoiceHeader>
    <invoiceDetails>
      <lineNumber>1</lineNumber>
      <quantity>1.00</quantity>
      <measurementUnit>1</measurementUnit>
      <netValue>100.00</netValue>
      <vatCategory>1</vatCategory>
      <vatAmount>24.00</vatAmount>
      <discountOption>false</discountOption>
      <lineComments>Test product</lineComments>
      <incomeClassification>
        <classificationType>E3_561_007</classificationType>
        <classificationCategory>category1_3</classificationCategory>
        <amount>100.00</amount>
      </incomeClassification>
    </invoiceDetails>
    <invoiceSummary>
      <totalNetValue>100.00</totalNetValue>
      <totalVatAmount>24.00</totalVatAmount>
      <totalWithheldAmount>0.00</totalWithheldAmount>
      <totalFeesAmount>0.00</totalFeesAmount>
      <totalStampDutyAmount>0.00</totalStampDutyAmount>
      <totalOtherTaxesAmount>0.00</totalOtherTaxesAmount>
      <totalDeductionsAmount>0.00</totalDeductionsAmount>
      <totalGrossValue>124.00</totalGrossValue>
    </invoiceSummary>
  </invoice>
</InvoicesDoc>`;
}