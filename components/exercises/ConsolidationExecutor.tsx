'use client'

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Play,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Database,
  Users,
  Image,
  Video,
  FileText,
  Shield
} from 'lucide-react';
import type { ConsolidationExercise } from './ConsolidationClient';

interface ConsolidationExecutorProps {
  exercises: ConsolidationExercise[];
  masterId: string;
  isLoading: boolean;
  onExecute: (consolidationData: ConsolidationResult) => Promise<void>;
}

export interface ConsolidationResult {
  masterId: string;
  mergedExerciseIds: string[];
  recordsMigrated: number;
  categoriesMerged: number;
  imagesMerged: number;
  videosMerged: number;
  timestamp: string;
}

type ExecutionStep = {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  icon: React.ComponentType<any>;
};

export default function ConsolidationExecutor({
  exercises,
  masterId,
  isLoading,
  onExecute
}: ConsolidationExecutorProps) {
  const supabase = createClientComponentClient<Database>();
  
  const [confirmationChecks, setConfirmationChecks] = useState({
    hasBackup: false,
    understandsImpact: false,
    cannotUndo: false
  });
  
  const [executionSteps, setExecutionSteps] = useState<ExecutionStep[]>([
    {
      id: 'validate',
      title: 'Validate Data',
      description: 'Checking data integrity and constraints',
      status: 'pending',
      icon: Shield
    },
    {
      id: 'records',
      title: 'Migrate Records',
      description: 'Moving performance records to master exercise',
      status: 'pending',
      icon: Users
    },
    {
      id: 'categories',
      title: 'Merge Categories',
      description: 'Consolidating category information',
      status: 'pending',
      icon: FileText
    },
    {
      id: 'media',
      title: 'Merge Media',
      description: 'Consolidating images and videos',
      status: 'pending',
      icon: Image
    },
    {
      id: 'cleanup',
      title: 'Cleanup',
      description: 'Archiving merged exercises and updating references',
      status: 'pending',
      icon: Database
    }
  ]);

  const [currentStep, setCurrentStep] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Check if all confirmations are checked
  const allConfirmed = Object.values(confirmationChecks).every(Boolean);

  // Get master exercise and exercises to merge
  const masterExercise = exercises.find(ex => ex.id === masterId);
  const exercisesToMerge = exercises.filter(ex => ex.id !== masterId);

  if (!masterExercise) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          Master exercise not found. Please go back and select a valid master exercise.
        </AlertDescription>
      </Alert>
    );
  }

  // Update step status
  const updateStepStatus = (stepId: string, status: ExecutionStep['status']) => {
    setExecutionSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status } : step
    ));
  };

  // Execute consolidation with step tracking
  const executeConsolidation = async () => {
    try {
      setError(null);
      setProgress(0);
      
      const totalSteps = executionSteps.length;
      let completedSteps = 0;

      // Step 1: Validate Data
      setCurrentStep('validate');
      updateStepStatus('validate', 'running');
      
      // Check for any blocking constraints
      const exerciseIds = exercisesToMerge.map(ex => ex.id);
      
      // Simulate validation delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateStepStatus('validate', 'completed');
      completedSteps++;
      setProgress((completedSteps / totalSteps) * 100);

      // Step 2: Migrate Records
      setCurrentStep('records');
      updateStepStatus('records', 'running');
      
      let totalRecordsMigrated = 0;
      for (const exercise of exercisesToMerge) {
        // Update all exercise_records to point to master exercise
        const { error: recordError } = await supabase
          .from('exercise_records')
          .update({ exercise_id: masterId })
          .eq('exercise_id', exercise.id);
          
        if (recordError) {
          throw new Error(`Failed to migrate records from ${exercise.exercise_name}: ${recordError.message}`);
        }
        
        totalRecordsMigrated += exercise.recordCount;
      }
      
      updateStepStatus('records', 'completed');
      completedSteps++;
      setProgress((completedSteps / totalSteps) * 100);

      // Step 3: Merge Categories
      setCurrentStep('categories');
      updateStepStatus('categories', 'running');
      
      let categoriesMerged = 0;
      for (const exercise of exercisesToMerge) {
        // Get categories that don't already exist for master
        const existingCategories = await supabase
          .from('exercise_categories')
          .select('category_type, category_value')
          .eq('exercise_id', masterId);
          
        const categoriesToAdd = exercise.categories.filter(cat => {
          return !existingCategories.data?.some(existing => 
            existing.category_type === cat.category_type && 
            existing.category_value === cat.category_value
          );
        });
        
        // Add unique categories to master
        if (categoriesToAdd.length > 0) {
          const { error: categoryError } = await supabase
            .from('exercise_categories')
            .insert(
              categoriesToAdd.map(cat => ({
                exercise_id: masterId,
                category_type: cat.category_type,
                category_value: cat.category_value,
                is_primary: cat.is_primary
              }))
            );
            
          if (categoryError) {
            console.warn(`Warning: Failed to merge some categories: ${categoryError.message}`);
          } else {
            categoriesMerged += categoriesToAdd.length;
          }
        }
      }
      
      updateStepStatus('categories', 'completed');
      completedSteps++;
      setProgress((completedSteps / totalSteps) * 100);

      // Step 4: Merge Media
      setCurrentStep('media');
      updateStepStatus('media', 'running');
      
      let imagesMerged = 0;
      let videosMerged = 0;
      
      for (const exercise of exercisesToMerge) {
        // Merge images (set is_primary to false to avoid conflicts)
        if (exercise.images.length > 0) {
          const { error: imageError } = await supabase
            .from('exercise_images')
            .insert(
              exercise.images.map(img => ({
                exercise_id: masterId,
                image_url: img.image_url,
                image_id: img.image_id,
                image_title: img.image_title,
                is_primary: false // Avoid primary conflicts
              }))
            );
            
          if (imageError) {
            console.warn(`Warning: Failed to merge some images: ${imageError.message}`);
          } else {
            imagesMerged += exercise.images.length;
          }
        }
        
        // Merge videos (set is_primary to false to avoid conflicts)
        if (exercise.videos.length > 0) {
          const { error: videoError } = await supabase
            .from('exercise_videos')
            .insert(
              exercise.videos.map(vid => ({
                exercise_id: masterId,
                video_url: vid.video_url,
                video_source: vid.video_source,
                video_title: vid.video_title,
                is_primary: false // Avoid primary conflicts
              }))
            );
            
          if (videoError) {
            console.warn(`Warning: Failed to merge some videos: ${videoError.message}`);
          } else {
            videosMerged += exercise.videos.length;
          }
        }
      }
      
      updateStepStatus('media', 'completed');
      completedSteps++;
      setProgress((completedSteps / totalSteps) * 100);

      // Step 5: Cleanup
      setCurrentStep('cleanup');
      updateStepStatus('cleanup', 'running');
      
      // Delete old exercise data (in reverse order to handle foreign keys)
      for (const exercise of exercisesToMerge) {
        // Delete related data first
        await Promise.all([
          supabase.from('exercise_videos').delete().eq('exercise_id', exercise.id),
          supabase.from('exercise_images').delete().eq('exercise_id', exercise.id),
          supabase.from('exercise_categories').delete().eq('exercise_id', exercise.id)
        ]);
        
        // Finally delete the exercise
        const { error: exerciseError } = await supabase
          .from('exercise_movements')
          .delete()
          .eq('id', exercise.id);
          
        if (exerciseError) {
          console.warn(`Warning: Failed to delete exercise ${exercise.exercise_name}: ${exerciseError.message}`);
        }
      }
      
      updateStepStatus('cleanup', 'completed');
      completedSteps++;
      setProgress(100);
      setCurrentStep(null);

      // Create consolidation result
      const result: ConsolidationResult = {
        masterId,
        mergedExerciseIds: exercisesToMerge.map(ex => ex.id),
        recordsMigrated: totalRecordsMigrated,
        categoriesMerged,
        imagesMerged,
        videosMerged,
        timestamp: new Date().toISOString()
      };

      // Call parent handler
      await onExecute(result);

    } catch (error) {
      console.error('Consolidation execution error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
      
      // Mark current step as error
      if (currentStep) {
        updateStepStatus(currentStep, 'error');
      }
      setCurrentStep(null);
    }
  };

  // Handle confirmation checkbox changes
  const handleConfirmationChange = (key: keyof typeof confirmationChecks, checked: boolean) => {
    setConfirmationChecks(prev => ({ ...prev, [key]: checked }));
  };

  const StepIcon = ({ step }: { step: ExecutionStep }) => {
    const Icon = step.icon;
    
    if (step.status === 'completed') {
      return <CheckCircle2 className="h-5 w-5 text-green-600" />;
    } else if (step.status === 'running') {
      return <Clock className="h-5 w-5 text-blue-600 animate-pulse" />;
    } else if (step.status === 'error') {
      return <AlertTriangle className="h-5 w-5 text-red-600" />;
    } else {
      return <Icon className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          <Play className="h-6 w-6 text-blue-600" />
          Execute Consolidation
        </h2>
        <p className="text-gray-600">
          Final step: confirm your choices and execute the consolidation.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-red-800">
            <strong>Execution Error:</strong> {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Execution Progress */}
      {isLoading && (
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Consolidation in Progress</h3>
              <span className="text-sm text-gray-600">{progress.toFixed(0)}%</span>
            </div>
            
            <Progress value={progress} className="w-full" />
            
            <div className="space-y-3">
              {executionSteps.map((step) => (
                <div key={step.id} className="flex items-center gap-3">
                  <StepIcon step={step} />
                  <div className="flex-1">
                    <div className="font-medium">{step.title}</div>
                    <div className="text-sm text-gray-600">{step.description}</div>
                  </div>
                  {step.status === 'running' && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Pre-execution confirmations */}
      {!isLoading && (
        <>
          {/* Final Summary */}
          <Card className="p-6 border-blue-200 bg-blue-50">
            <h3 className="text-lg font-semibold text-blue-800 mb-4">Final Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium text-blue-900 mb-2">Master Exercise</div>
                <div className="text-blue-800">{masterExercise.exercise_name}</div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Exercises to Remove</div>
                <div className="text-blue-800">{exercisesToMerge.length} exercises</div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Records to Migrate</div>
                <div className="text-blue-800">
                  {exercises.reduce((sum, ex) => sum + ex.recordCount, 0)} total records
                </div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Media to Merge</div>
                <div className="text-blue-800">
                  {exercises.reduce((sum, ex) => sum + ex.images.length + ex.videos.length, 0)} files
                </div>
              </div>
            </div>
          </Card>

          {/* Safety Confirmations */}
          <Card className="p-6 border-orange-200 bg-orange-50">
            <h3 className="text-lg font-semibold text-orange-800 mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Safety Confirmations
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Checkbox
                  id="backup"
                  checked={confirmationChecks.hasBackup}
                  onCheckedChange={(checked) => handleConfirmationChange('hasBackup', checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="backup" className="text-orange-800 font-medium cursor-pointer">
                    I have verified that a recent backup exists
                  </Label>
                  <p className="text-sm text-orange-700">
                    Ensure you have a database backup from within the last 24 hours.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="impact"
                  checked={confirmationChecks.understandsImpact}
                  onCheckedChange={(checked) => handleConfirmationChange('understandsImpact', checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="impact" className="text-orange-800 font-medium cursor-pointer">
                    I understand the impact on users and data
                  </Label>
                  <p className="text-sm text-orange-700">
                    Users will no longer see the merged exercises and all records will be consolidated.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="undo"
                  checked={confirmationChecks.cannotUndo}
                  onCheckedChange={(checked) => handleConfirmationChange('cannotUndo', checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="undo" className="text-orange-800 font-medium cursor-pointer">
                    I understand this action cannot be easily undone
                  </Label>
                  <p className="text-sm text-orange-700">
                    While data can be restored from backup, the consolidation process is not reversible.
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Execution Steps Preview */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Execution Steps</h3>
            <div className="space-y-3">
              {executionSteps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-600 text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{step.title}</div>
                    <div className="text-sm text-gray-600">{step.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Execute Button */}
          <Card className="p-6 text-center">
            <Button
              onClick={executeConsolidation}
              disabled={!allConfirmed || isLoading}
              size="lg"
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Play className="h-5 w-5 mr-2" />
              Execute Consolidation
            </Button>
            
            {!allConfirmed && (
              <p className="mt-3 text-sm text-gray-600">
                Please complete all safety confirmations above to proceed.
              </p>
            )}
            
            <p className="mt-3 text-sm text-gray-500">
              This process typically takes 30-60 seconds depending on the amount of data.
            </p>
          </Card>
        </>
      )}
    </div>
  );
}900 mb-2">Master Exercise</div>
                <div className="text-blue-800">{masterExercise.exercise_name}</div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Exercises to Remove</div>
                <div className="text-blue-800">{exercisesToMerge.length} exercises</div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Records to Migrate</div>
                <div className="text-blue-800">
                  {exercises.reduce((sum, ex) => sum + ex.recordCount, 0)} total records
                </div>
              </div>
              <div>
                <div className="font-medium text-blue-900 mb-2">Media to Merge</div>
                <div className="text-blue-800">
                  {exercises.reduce((sum, ex) => sum + ex.images.length + ex.videos.length, 0)} files
                </div>
              </div>
            </div>
          </Card>

          {/* Safety Confirmations */}
          <Card className="p-6 border-orange-200 bg-orange-50">
            <h3 className="text-lg font-semibold text-orange-800 mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Safety Confirmations
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Checkbox
                  id="backup"
                  checked={confirmationChecks.hasBackup}
                  onCheckedChange={(checked) => handleConfirmationChange('hasBackup', checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="backup" className="text-orange-800 font-medium cursor-pointer">
                    I have verified that a recent backup exists
                  </Label>
                  <p className="text-sm text-orange-700">
                    Ensure you have a database backup from within the last 24 hours.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="impact"
                  checked={confirmationChecks.understandsImpact}
                  onCheckedChange={(checked) => handleConfirmationChange('understandsImpact', checked as boolean)}
                />
                <div className="space-y-1"> 
                  <Label htmlFor="impact" className="text-orange-800 font-medium cursor-pointer">
                    I understand the impact on users and data
                  </Label>
                  <p className="text-sm text-orange-700">
                    Users will no longer see the merged exercises and all records will be consolidated.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="undo"
                  checked={confirmationChecks.cannotUndo}
                  onCheckedChange={(checked) => handleConfirmationChange('cannotUndo', checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="undo" className="text-orange-800 font-medium cursor-pointer">
                    I understand this action cannot be easily undone
                  </Label>
                  <p className="text-sm text-orange-700">
                    While data can be restored from backup, the consolidation process is not reversible.
                    </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Execution Steps Preview */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Execution Steps</h3>
            <div className="space-y-3">
              {executionSteps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-600 text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{step.title}</div>
                    <div className="text-sm text-gray-600">{step.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Execute Button */}
          <Card className="p-6 text-center">
            <Button
              onClick={executeConsolidation}
              disabled={!allConfirmed || isLoading}
              size="lg"
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Play className="h-5 w-5 mr-2" />
              Execute Consolidation
            </Button>
            
            {!allConfirmed && (
              <p className="mt-3 text-sm text-gray-600">
                Please complete all safety confirmations above to proceed.
              </p>
            )}
            
            <p className="mt-3 text-sm text-gray-500">
              This process typically takes 30-60 seconds depending on the amount of data.
            </p>
          </Card>
        </>
      )}
    </div>
  );
}