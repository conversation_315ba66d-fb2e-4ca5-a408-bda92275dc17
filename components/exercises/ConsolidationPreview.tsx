'use client'

import { useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Eye,
  Crown,
  Users, 
  Image, 
  Video, 
  FileText,
  ArrowRight,
  AlertTriangle,
  Info,
  Trash2,
  Database
} from 'lucide-react';
import SafeImage from '@/components/ui/SafeImage';
import { getDirectImageUrl, getImageUrl } from '@/lib/utils/imageUtils';
import type { ConsolidationExercise } from './ConsolidationClient';

interface ConsolidationPreviewProps {
  exercises: ConsolidationExercise[];
  masterId: string;
}

export default function ConsolidationPreview({
  exercises,
  masterId
}: ConsolidationPreviewProps) {
  
  // Split exercises into master and others
  const masterExercise = exercises.find(ex => ex.id === masterId);
  const exercisesToMerge = exercises.filter(ex => ex.id !== masterId);

  if (!masterExercise) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          Master exercise not found. Please go back and select a valid master exercise.
        </AlertDescription>
      </Alert>
    );
  }

  // Calculate consolidated data
  const consolidatedData = useMemo(() => {
    const allCategories = exercises.flatMap(ex => ex.categories);
    const allImages = exercises.flatMap(ex => ex.images);
    const allVideos = exercises.flatMap(ex => ex.videos);
    const totalRecords = exercises.reduce((sum, ex) => sum + ex.recordCount, 0);

    // Merge categories (avoiding duplicates)
    const uniqueCategories = allCategories.filter((category, index, self) => 
      index === self.findIndex(c => 
        c.category_type === category.category_type && 
        c.category_value === category.category_value
      )
    );

    // Handle primary image/video conflicts
    const primaryImages = allImages.filter(img => img.is_primary);
    const primaryVideos = allVideos.filter(vid => vid.is_primary);

    return {
      categories: uniqueCategories,
      images: allImages,
      videos: allVideos,
      totalRecords,
      conflicts: {
        multiplePrimaryImages: primaryImages.length > 1,
        multiplePrimaryVideos: primaryVideos.length > 1,
        categoryConflicts: [] // We'll calculate this if needed
      }
    };
  }, [exercises]);

  // Helper to get primary image URL
  const getPrimaryImageUrl = (exercise: ConsolidationExercise): string | null => {
    const primaryImage = exercise.images.find(img => img.is_primary) || exercise.images[0];
    if (!primaryImage) return null;

    return primaryImage.image_url 
      ? getDirectImageUrl(primaryImage.image_url)
      : getImageUrl(primaryImage.image_id || '');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          <Eye className="h-6 w-6 text-blue-600" />
          Preview Consolidation
        </h2>
        <p className="text-gray-600">
          Review the changes that will be made before executing the consolidation.
        </p>
      </div>

      {/* Conflicts Warning */}
      {(consolidatedData.conflicts.multiplePrimaryImages || consolidatedData.conflicts.multiplePrimaryVideos) && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-orange-800">
            <strong>Conflicts Detected:</strong>
            {consolidatedData.conflicts.multiplePrimaryImages && (
              <span className="block">• Multiple exercises have primary images. The master's primary image will be kept.</span>
            )}
            {consolidatedData.conflicts.multiplePrimaryVideos && (
              <span className="block">• Multiple exercises have primary videos. The master's primary video will be kept.</span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Master Exercise Display */}
      <Card className="p-6 border-green-200 bg-green-50">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <Badge className="bg-green-100 text-green-800 border-green-200 mb-3">
              <Crown className="h-3 w-3 mr-1" />
              Master Exercise (Kept)
            </Badge>
            {getPrimaryImageUrl(masterExercise) ? (
              <SafeImage
                src={getPrimaryImageUrl(masterExercise)!}
                alt={masterExercise.exercise_name}
                width={100}
                height={100}
                className="rounded-lg object-cover"
              />
            ) : (
              <div className="w-25 h-25 bg-gray-100 rounded-lg flex items-center justify-center">
                <Image className="h-10 w-10 text-gray-400" />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-green-800 mb-2">
              {masterExercise.exercise_name}
            </h3>
            {masterExercise.description && (
              <p className="text-green-700 mb-3">{masterExercise.description}</p>
            )}
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-600" />
                <span>Records: {masterExercise.recordCount}</span>
              </div>
              <div className="flex items-center gap-2">
                <Image className="h-4 w-4 text-green-600" />
                <span>Images: {masterExercise.images.length}</span>
              </div>
              <div className="flex items-center gap-2">
                <Video className="h-4 w-4 text-green-600" />
                <span>Videos: {masterExercise.videos.length}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-green-600" />
                <span>Categories: {masterExercise.categories.length}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Exercises to be Merged */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Trash2 className="h-5 w-5 text-red-600" />
          Exercises to be Merged & Removed
        </h3>
        
        <div className="space-y-3">
          {exercisesToMerge.map((exercise) => (
            <Card key={exercise.id} className="p-4 border-red-200 bg-red-50">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  {getPrimaryImageUrl(exercise) ? (
                    <SafeImage
                      src={getPrimaryImageUrl(exercise)!}
                      alt={exercise.exercise_name}
                      width={60}
                      height={60}
                      className="rounded object-cover"
                    />
                  ) : (
                    <div className="w-15 h-15 bg-gray-100 rounded flex items-center justify-center">
                      <Image className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-red-800">{exercise.exercise_name}</h4>
                    <ArrowRight className="h-4 w-4 text-red-600" />
                    <span className="text-sm text-red-600">Will be removed</span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm text-red-700">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{exercise.recordCount} records</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Image className="h-3 w-3" />
                      <span>{exercise.images.length} images</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Video className="h-3 w-3" />
                      <span>{exercise.videos.length} videos</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      <span>{exercise.categories.length} categories</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Consolidated Result Preview */}
      <Card className="p-6 border-blue-200 bg-blue-50">
        <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2">
          <Database className="h-5 w-5" />
          Final Consolidated Exercise
        </h3>
        
        <div className="space-y-4">
          <div>
            <div className="font-medium text-blue-900 mb-2">Exercise Details</div>
            <div className="text-blue-800">
              <div>Name: {masterExercise.exercise_name}</div>
              {masterExercise.description && (
                <div>Description: {masterExercise.description}</div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="font-medium text-blue-900 mb-2">Performance Data</div>
              <div className="space-y-1 text-sm text-blue-800">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Total Records: {consolidatedData.totalRecords}</span>
                  <Badge variant="outline" className="text-xs">
                    +{consolidatedData.totalRecords - masterExercise.recordCount} merged
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <div className="font-medium text-blue-900 mb-2">Media Content</div>
              <div className="space-y-1 text-sm text-blue-800">
                <div className="flex items-center gap-2">
                  <Image className="h-4 w-4" />
                  <span>Total Images: {consolidatedData.images.length}</span>
                  {consolidatedData.images.length > masterExercise.images.length && (
                    <Badge variant="outline" className="text-xs">
                      +{consolidatedData.images.length - masterExercise.images.length} merged
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  <span>Total Videos: {consolidatedData.videos.length}</span>
                  {consolidatedData.videos.length > masterExercise.videos.length && (
                    <Badge variant="outline" className="text-xs">
                      +{consolidatedData.videos.length - masterExercise.videos.length} merged
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div>
            <div className="font-medium text-blue-900 mb-2">Categories</div>
            <div className="flex flex-wrap gap-1">
              {consolidatedData.categories
                .filter(cat => cat.is_primary)
                .slice(0, 8)
                .map((category, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {category.category_type}: {category.category_value}
                  </Badge>
                ))}
              {consolidatedData.categories.filter(cat => cat.is_primary).length > 8 && (
                <Badge variant="outline" className="text-xs">
                  +{consolidatedData.categories.filter(cat => cat.is_primary).length - 8} more
                </Badge>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Impact Summary */}
      <Alert className="border-blue-200 bg-blue-50">
        <Info className="h-4 w-4" />
        <AlertDescription className="text-blue-800">
          <strong>Impact Summary:</strong>
          <ul className="mt-2 space-y-1 list-disc list-inside">
            <li>{exercisesToMerge.length} exercise{exercisesToMerge.length > 1 ? 's' : ''} will be removed from the system</li>
            <li>{consolidatedData.totalRecords} performance records will be transferred to the master exercise</li>
            <li>{consolidatedData.images.length + consolidatedData.videos.length} media files will be consolidated</li>
            <li>All references in workout plans and programs will be updated automatically</li>
            <li>Users will see only the master exercise in their exercise selection</li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* Final Warning */}
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          <strong>Warning:</strong> This action cannot be easily undone. Ensure you have a recent backup 
          before proceeding. The removed exercises will be archived but their individual identity will be lost.
        </AlertDescription>
      </Alert>
    </div>
  );
}