'use client'

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  BODY_PARTS,
  EQUIPMENT,
  EXPERTISE_LEVELS,
  MOVEMENT_CATEGORIES,
  VIDEO_SOURCES,
  Filters
} from './ExercisesClient';

interface ExerciseFiltersProps {
  searchTerm: string;
  filters: Filters;
  onSearchChange: (value: string) => void;
  onFilterChange: (filters: Filters) => void;
  onClearFilters: () => void;
}

export default function ExerciseFilters({
  searchTerm,
  filters,
  onSearchChange,
  onFilterChange,
  onClearFilters
}: ExerciseFiltersProps) {

  // Handle individual filter changes
  const handleFilterChange = (name: keyof Filters, value: string) => {
    onFilterChange({
      ...filters,
      [name]: value
    });
  };

  return (
    <div className="mb-6 space-y-4">
      <div className="flex gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search exercises..."
            value={searchTerm}
            onChange={(e) => {
              console.log("Search input changed to:", e.target.value);
              onSearchChange(e.target.value);
            }}
            className="w-full"
          />
        </div>
        <Button
          variant="outline"
          onClick={onClearFilters}
        >
          Clear Filters
        </Button>
      </div>

      {/* Basic Category Filters */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Select
          value={filters.body_part}
          onValueChange={(value) => handleFilterChange('body_part', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Body Part" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Body Parts</SelectItem>
            {BODY_PARTS.map((part) => (
              <SelectItem key={part} value={part}>
                {part}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.equipment}
          onValueChange={(value) => handleFilterChange('equipment', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Equipment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Equipment</SelectItem>
            {EQUIPMENT.map((item) => (
              <SelectItem key={item} value={item}>
                {item}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.expertise_level}
          onValueChange={(value) => handleFilterChange('expertise_level', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            {EXPERTISE_LEVELS.map((level) => (
              <SelectItem key={level} value={level}>
                {level}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.movement_category}
          onValueChange={(value) => handleFilterChange('movement_category', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {MOVEMENT_CATEGORIES.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Media and Record Filters */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Select
          value={filters.has_video}
          onValueChange={(value) => handleFilterChange('has_video', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Video" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Exercises</SelectItem>
            <SelectItem value="yes">Has Video</SelectItem>
            <SelectItem value="no">No Video</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.has_image}
          onValueChange={(value) => handleFilterChange('has_image', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Image" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Exercises</SelectItem>
            <SelectItem value="yes">Has Image</SelectItem>
            <SelectItem value="no">No Image</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.has_records}
          onValueChange={(value) => handleFilterChange('has_records', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Records" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Exercises</SelectItem>
            <SelectItem value="yes">Has Records</SelectItem>
            <SelectItem value="no">No Records</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.video_source}
          onValueChange={(value) => handleFilterChange('video_source', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by Source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Sources</SelectItem>
            {VIDEO_SOURCES.map((source) => (
              <SelectItem key={source} value={source}>
                {source}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}