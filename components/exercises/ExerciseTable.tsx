// ExerciseTable.tsx should contain:
'use client'

// UI components are imported but used in the JSX structure
// without direct variable references
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronDown, ChevronUp, ChevronsUpDown, Info } from 'lucide-react';
import { ExerciseMovement, SortConfig } from './ExercisesClient';
import MediaIndicator from './MediaIndicator';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useRef } from 'react';
import NextImage from 'next/image';
import { getImageUrl, getFallbackImageUrl } from '@/lib/utils/imageUtils';

interface ExerciseTableProps {
  exercises: ExerciseMovement[];
  sortConfig: SortConfig;
  // These props are passed but not used in the current implementation
  // They are kept for future pagination implementation
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalExercises: number;
  totalAllExercises: number;
  onSort: (key: string) => void;
  onEdit: (exercise: ExerciseMovement) => void;
  onDelete: (id: string) => void;
  onOpenVideoPreview: (url: string, source: string) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  isLoading?: boolean;
  // Virtualization props
  containerHeight?: number;
}

export default function ExerciseTable({
  exercises,
  sortConfig,
  // These props are declared but not used in the current implementation
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  currentPage,
  pageSize,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  totalPages,
  totalExercises,
  totalAllExercises,
  onSort,
  onEdit,
  onDelete,
  onOpenVideoPreview,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onPageChange,
  onPageSizeChange,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isLoading = false,
  containerHeight = 600 // Default height for virtualization
}: ExerciseTableProps) {

  // Get sort direction icon
  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return <ChevronsUpDown className="h-4 w-4" />;
    return sortConfig.direction === 'asc'
      ? <ChevronUp className="h-4 w-4" />
      : <ChevronDown className="h-4 w-4" />;
  };

  // Helper to get primary category value
  const getPrimaryCategoryValue = (exercise: ExerciseMovement, categoryType: string) => {
    return exercise.categories?.find(cat =>
      cat.category_type === categoryType && cat.is_primary
    )?.category_value || '';
  };

  // Helper to get primary image

  const getPrimaryImage = (exercise: ExerciseMovement) => {
    return exercise.images?.find(image => image.is_primary) || exercise.images?.[0];
  };

  // Reference for the scrollable container
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Set up virtualization for performance
  const rowVirtualizer = useVirtualizer({
    count: exercises.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 60, // Estimated row height
    overscan: 5,
  });

  return (
    <div>
      {/* Single scrollable container with border */}
      <div
        ref={tableContainerRef}
        className="border rounded-lg relative"
        style={{ height: containerHeight, overflow: 'auto' }}
      >
        {/* Sticky table header */}
        <div className="sticky top-0 bg-white z-10">
          <table className="w-full caption-bottom text-sm">
            <thead className="[&_tr]:border-b">
              <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <th className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">Thumbnail</th>
                <th
                  className="h-10 px-2 text-left align-middle font-medium text-muted-foreground cursor-pointer"
                  onClick={() => onSort('exercise_name')}
                >
                  <div className="flex items-center gap-2">
                    Exercise Name
                    {getSortIcon('exercise_name')}
                  </div>
                </th>
                <th
                  className="h-10 px-2 text-left align-middle font-medium text-muted-foreground cursor-pointer"
                  onClick={() => onSort('body_part')}
                >
                  <div className="flex items-center gap-2">
                    Body Part
                    {getSortIcon('body_part')}
                  </div>
                </th>
                <th
                  className="h-10 px-2 text-left align-middle font-medium text-muted-foreground cursor-pointer"
                  onClick={() => onSort('equipment')}
                >
                  <div className="flex items-center gap-2">
                    Equipment
                    {getSortIcon('equipment')}
                  </div>
                </th>
                <th
                  className="h-10 px-2 text-left align-middle font-medium text-muted-foreground cursor-pointer"
                  onClick={() => onSort('expertise_level')}
                >
                  <div className="flex items-center gap-2">
                    Expertise Level
                    {getSortIcon('expertise_level')}
                  </div>
                </th>
                <th
                  className="h-10 px-2 text-left align-middle font-medium text-muted-foreground cursor-pointer"
                  onClick={() => onSort('movement_category')}
                >
                  <div className="flex items-center gap-2">
                    Movement Category
                    {getSortIcon('movement_category')}
                  </div>
                </th>
                <th className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">Media</th>
                <th className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
          </table>
        </div>

        {/* Virtualized content container */}
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map(virtualRow => {
            const exercise = exercises[virtualRow.index];
            return (
              <div
                key={exercise.id}
                className={`flex w-full border-b ${exercise.hasRecords ? "bg-blue-50 border-l-4 border-blue-500" : ""}`}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualRow.start}px)`,
                  display: 'grid',
                  gridTemplateColumns: 'auto 1fr auto auto auto auto auto auto',
                  alignItems: 'center',
                }}
              >
                {/* Thumbnail cell */}
                <div className="p-2 w-16">
                  {getPrimaryImage(exercise) ? (
                    <div>
                      <div className="relative w-12 h-12 rounded-md overflow-hidden">
                        <NextImage
                          src={getImageUrl(getPrimaryImage(exercise)?.image_id || '')}
                          alt={exercise.exercise_name}
                          className="object-cover"
                          fill
                          sizes="48px"
                          onError={(e) => {
                            console.error('Image load error:', {
                              imageId: getPrimaryImage(exercise)?.image_id,
                              generatedUrl: getImageUrl(getPrimaryImage(exercise)?.image_id || '')
                            });
                            const imgElement = e.currentTarget as HTMLImageElement;
                            imgElement.src = getFallbackImageUrl();
                          }}
                        />
                      </div>
                      <div className="mt-1 text-xs text-gray-500 truncate" title={getPrimaryImage(exercise)?.image_id || ''}>
                        ID: {getPrimaryImage(exercise)?.image_id?.substring(0, 10)}...
                      </div>
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 flex items-center justify-center rounded-md">
                      <span className="text-xs text-gray-400">No image</span>
                    </div>
                  )}
                </div>

                <div className="p-2">
                  <div className="flex flex-col">
                    <span className="font-medium">{exercise.exercise_name}</span>
                    {exercise.hasRecords && (
                      <Badge variant="outline" className="mt-1 bg-blue-100 text-blue-800 font-medium text-xs">
                        Has Records
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="p-2">{getPrimaryCategoryValue(exercise, 'body_part')}</div>
                <div className="p-2">{getPrimaryCategoryValue(exercise, 'equipment')}</div>
                <div className="p-2">{getPrimaryCategoryValue(exercise, 'expertise_level')}</div>
                <div className="p-2">{getPrimaryCategoryValue(exercise, 'movement_category')}</div>
                <div className="p-2">
                  <div className="flex space-x-2">
                    <MediaIndicator
                      videos={exercise.videos || []}
                      images={exercise.images || []}
                      onVideoClick={(video) => onOpenVideoPreview(video.video_url, video.video_source || '')}
                    />
                  </div>
                </div>
                <div className="p-2">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(exercise)}
                    >
                      Edit
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="sm"
                          disabled={exercise.hasRecords}
                          title={exercise.hasRecords ? "Cannot delete exercises with records" : "Delete exercise"}
                        >
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Exercise</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete this exercise? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => onDelete(exercise.id)}>
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty state message */}
        {exercises.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No exercises found matching your filters. Try adjusting your search criteria.
          </div>
        )}
      </div>

      {/* Simplified Pagination Controls */}
      {totalExercises > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {totalExercises} filtered exercises (from total {totalAllExercises})
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Display Count:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => onPageSizeChange(Number(value))}
            >
              <SelectTrigger className="w-16">
                <SelectValue placeholder="50" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
                <SelectItem value="500">500</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      <div className="mt-4 text-sm text-gray-700 flex items-center">
        <Info className="h-4 w-4 mr-2" />
        Exercises with <span className="bg-blue-50 border-l-4 border-blue-500 px-1 rounded">blue background and left border</span> have associated performance records and cannot be deleted.
      </div>
    </div>
  );
}