'use client'

import { useState, useTransition, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import ExerciseTable from './ExerciseTable';
import ExerciseFilters from './ExerciseFilters';
import ExerciseForm from './ExerciseForm';
import SearchBar from './SearchBar';
import MediaPreviewDialog from './MediaPreviewDialog';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import Link from 'next/link';
import { Database } from 'lucide-react';


// Enhanced types with proper error handling
export type ExerciseMovement = Database['public']['Tables']['exercise_movements']['Row'] & {
  hasRecords?: boolean;
  videos?: ExerciseVideo[];
  images?: ExerciseImage[];
  categories?: ExerciseCategory[];
};

export type ExerciseVideo = Database['public']['Tables']['exercise_videos']['Row'];
export type ExerciseImage = Database['public']['Tables']['exercise_images']['Row'];
export type ExerciseCategory = Database['public']['Tables']['exercise_categories']['Row'];

export type SortConfig = {
  key: string;
  direction: 'asc' | 'desc';
} | null;

export type Filters = {
  body_part: string;
  equipment: string;
  expertise_level: string;
  movement_category: string;
  has_video: string;
  has_image: string;
  has_records: string;
  video_source: string;
};

// Constants
export const EXPERTISE_LEVELS = ['Beginner', 'Intermediate', 'Advanced', 'Expert', 'Elite'];
export const MOVEMENT_CATEGORIES = ['Strength', 'Cardio', 'Gymnastics', 'Olympic Lifting', 'Conditioning'];
export const BODY_PARTS = ['Upper Body', 'Lower Body', 'Core', 'Full Body'];
export const EQUIPMENT = ['Barbell', 'Dumbbell', 'Kettlebell', 'Bodyweight', 'Machine', 'Ab wheel', 'Medicine ball', 'Other'];
export const VIDEO_SOURCES = ['youtube', 'sweat.com', 'trainwell.com', 'other'];

interface ExercisesClientProps {
  exercises: ExerciseMovement[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  searchParams: Record<string, string>;
}

export default function ExercisesClient({ 
  exercises, 
  totalCount, 
  currentPage, 
  pageSize, 
  totalPages,
  searchParams 
}: ExercisesClientProps) {
  const router = useRouter();
  const currentSearchParams = useSearchParams();
  const supabase = createClientComponentClient<Database>();
  const queryClient = useQueryClient();
  
  // State management
  const [isPending, startTransition] = useTransition();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState<ExerciseMovement | null>(null);
  const [currentVideoPreview, setCurrentVideoPreview] = useState<{url: string, source: string} | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Current filter values from URL
  const currentFilters: Filters = {
    body_part: searchParams.body_part || 'all',
    equipment: searchParams.equipment || 'all',
    expertise_level: searchParams.expertise_level || 'all',
    movement_category: searchParams.movement_category || 'all',
    has_video: searchParams.has_video || 'all',
    has_image: searchParams.has_image || 'all',
    has_records: searchParams.has_records || 'all',
    video_source: searchParams.video_source || 'all',
  };

  const currentSearchTerm = searchParams.search || '';
  const currentSort = searchParams.sort || 'exercise_name';
  const currentOrder = (searchParams.order as 'asc' | 'desc') || 'asc';

  // URL update function
  const updateURL = useCallback((updates: Record<string, string | null>) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '' || value === 'all') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Reset to page 1 when filters change (except for page navigation)
    if (!updates.page) {
      params.delete('page');
    }

    const newURL = `?${params.toString()}`;
    startTransition(() => {
      router.push(newURL);
    });
  }, [currentSearchParams, router]);

  // Search handler
  const handleSearch = useCallback((searchTerm: string) => {
    updateURL({ search: searchTerm });
  }, [updateURL]);

  // Filter change handler
  const handleFilterChange = useCallback((newFilters: Partial<Filters>) => {
    updateURL(newFilters);
  }, [updateURL]);

  // Sort handler
  const handleSort = useCallback((key: string) => {
    const newOrder = (currentSort === key && currentOrder === 'asc') ? 'desc' : 'asc';
    updateURL({ sort: key, order: newOrder });
  }, [currentSort, currentOrder, updateURL]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    updateURL({ page: page.toString() });
  }, [updateURL]);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    updateURL({ limit: newPageSize.toString() });
  }, [updateURL]);

  // Clear filters
  const handleClearFilters = useCallback(() => {
    updateURL({
      search: null,
      body_part: null,
      equipment: null,
      expertise_level: null,
      movement_category: null,
      has_video: null,
      has_image: null,
      has_records: null,
      video_source: null,
      sort: null,
      order: null
    });
  }, [updateURL]);

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      // Check if exercise has records
      const { data: recordsData, error: recordsError } = await supabase
        .from('exercise_records')
        .select('id')
        .eq('exercise_id', id)
        .limit(1);

      if (recordsError) throw recordsError;

      if (recordsData && recordsData.length > 0) {
        throw new Error('Cannot delete exercise with associated records');
      }

      // Delete related data first
      await Promise.all([
        supabase.from('exercise_videos').delete().eq('exercise_id', id),
        supabase.from('exercise_images').delete().eq('exercise_id', id),
        supabase.from('exercise_categories').delete().eq('exercise_id', id)
      ]);

      // Then delete the exercise
      const { error } = await supabase.from('exercise_movements').delete().eq('id', id);
      if (error) throw error;
    },
    onSuccess: () => {
      // Refresh the page to get updated data
      router.refresh();
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    }
  });

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: async (exercise: ExerciseMovement) => {
      const isNew = !exercise.id;

      if (isNew) {
        // Create new exercise
        const { data, error } = await supabase
          .from('exercise_movements')
          .insert({
            exercise_name: exercise.exercise_name,
            description: exercise.description,
            body_part: '',
            equipment: '',
            expertise_level: '',
            movement_category: ''
          })
          .select();

        if (error) throw error;
        if (!data || data.length === 0) throw new Error('Failed to create exercise');

        const newExerciseId = data[0].id;
        await saveRelatedData(exercise, newExerciseId);
      } else {
        // Update existing exercise
        const { error } = await supabase
          .from('exercise_movements')
          .update({
            exercise_name: exercise.exercise_name,
            description: exercise.description
          })
          .eq('id', exercise.id);

        if (error) throw error;
        await saveRelatedData(exercise, exercise.id);
      }
    },
    onSuccess: () => {
      setIsModalOpen(false);
      setSelectedExercise(null);
      router.refresh();
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    }
  });

  // Save related data helper
  const saveRelatedData = async (exercise: ExerciseMovement, exerciseId: string) => {
    // First, delete existing related data
    await Promise.all([
      supabase.from('exercise_videos').delete().eq('exercise_id', exerciseId),
      supabase.from('exercise_images').delete().eq('exercise_id', exerciseId),
      supabase.from('exercise_categories').delete().eq('exercise_id', exerciseId)
    ]);

    // Then insert new data
    const videos = exercise.videos || [];
    if (videos.length > 0) {
      await supabase.from('exercise_videos').insert(
        videos.map(video => ({ ...video, exercise_id: exerciseId }))
      );
    }

    const images = exercise.images || [];
    if (images.length > 0) {
      await supabase.from('exercise_images').insert(
        images.map(image => ({ ...image, exercise_id: exerciseId }))
      );
    }

    const categories = exercise.categories || [];
    if (categories.length > 0) {
      await supabase.from('exercise_categories').insert(
        categories.map(category => ({ ...category, exercise_id: exerciseId }))
      );
    }
  };

  // Event handlers
  const handleEdit = (exercise: ExerciseMovement) => {
    setSelectedExercise(exercise);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this exercise?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleOpenVideoPreview = (url: string, source: string = '') => {
    if (!url) return;
    setCurrentVideoPreview({ url, source });
  };

  const handleCloseVideoPreview = () => {
    setCurrentVideoPreview(null);
  };

  const handleSaveExercise = (exercise: ExerciseMovement) => {
    saveMutation.mutate(exercise);
  };

  // Create sort config for table
  const sortConfig: SortConfig = {
    key: currentSort,
    direction: currentOrder
  };

  return (
    <div className="container mx-auto py-10">
      {/* Error Alert */}
      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

{/* Header */}
<div className="flex justify-between items-center mb-6">
  <div>
    <h1 className="text-2xl font-bold">Exercise Movements</h1>
    <p className="text-sm text-gray-500">
      {currentSearchTerm
        ? `Found ${totalCount} exercises matching "${currentSearchTerm}"`
        : `Total: ${totalCount} exercises`}
    </p>
  </div>
  <div className="flex gap-2">
    {/* Admin-only consolidation button */}
    <Button variant="outline" asChild>
      <Link href="/admin/exercises/consolidate">
        <Database className="h-4 w-4 mr-2" />
        Manage Duplicates
      </Link>
    </Button>
    
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogTrigger asChild>
        <Button onClick={() => setSelectedExercise(null)}>Add New Exercise</Button>
      </DialogTrigger>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedExercise ? 'Edit Exercise' : 'Add New Exercise'}
              </DialogTitle>
            </DialogHeader>
            <ExerciseForm
              exercise={selectedExercise}
              onSave={handleSaveExercise}
              onCancel={() => setIsModalOpen(false)}
              isLoading={saveMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search Bar */}
      <Card className="p-4 mb-4">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="flex-grow w-full md:w-auto">
            <SearchBar
              onSearch={handleSearch}
              initialValue={currentSearchTerm}
              isSearching={isPending}
              placeholder="Search exercises by name, description, or category..."
            />
          </div>
          <Button
            variant="outline"
            onClick={handleClearFilters}
            disabled={isPending}
            className="whitespace-nowrap"
          >
            Clear All Filters
          </Button>
        </div>
        {currentSearchTerm && (
          <div className="mt-2 text-sm text-gray-500">
            <p>
              Showing {exercises.length} of {totalCount} results on page {currentPage} of {totalPages}.
            </p>
          </div>
        )}
      </Card>

      {/* Loading Overlay */}
      {isPending && (
        <Card className="p-4 mb-4 bg-blue-50 border-blue-200">
          <div className="flex items-center gap-2 text-blue-800">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Updating results...</span>
          </div>
        </Card>
      )}

      {/* Filters */}
      <ExerciseFilters
        searchTerm={currentSearchTerm}
        filters={currentFilters}
        onSearchChange={handleSearch}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        disabled={isPending}
      />

      {/* Table */}
      <ExerciseTable
        exercises={exercises}
        sortConfig={sortConfig}
        currentPage={currentPage}
        pageSize={pageSize}
        totalPages={totalPages}
        totalExercises={totalCount}
        totalAllExercises={totalCount}
        onSort={handleSort}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onOpenVideoPreview={handleOpenVideoPreview}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        isLoading={isPending || deleteMutation.isPending}
      />

      {/* Media Preview Dialog */}
      {currentVideoPreview && (
        <MediaPreviewDialog
          mediaItem={currentVideoPreview}
          onClose={handleCloseVideoPreview}
        />
      )}
    </div>
  );
}