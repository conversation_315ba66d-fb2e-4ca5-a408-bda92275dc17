'use client'

import { useState, useMemo, useReducer } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useDebounce } from '@/hooks/useDebounce';
import ExerciseTable from './ExerciseTable';
import ExerciseFilters from './ExerciseFilters';
import ExerciseForm from './ExerciseForm';
import SearchBar from './SearchBar';
import MediaPreviewDialog from './MediaPreviewDialog';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";

// Define extended types based on the normalized data model
export type ExerciseMovement = Database['public']['Tables']['exercise_movements']['Row'] & {
  hasRecords?: boolean;
  videos?: ExerciseVideo[];
  images?: ExerciseImage[];
  categories?: ExerciseCategory[];
};

export type ExerciseVideo = Database['public']['Tables']['exercise_videos']['Row'];
export type ExerciseImage = Database['public']['Tables']['exercise_images']['Row'];
export type ExerciseCategory = Database['public']['Tables']['exercise_categories']['Row'];

export type SortConfig = {
  key: string;
  direction: 'asc' | 'desc';
} | null;

export type Filters = {
  body_part: string | 'all';
  equipment: string | 'all';
  expertise_level: string | 'all';
  movement_category: string | 'all';
  has_video: string | 'all';
  has_image: string | 'all';
  has_records: string | 'all';
  video_source: string | 'all';
};

// Constants for filters and categories
export const EXPERTISE_LEVELS = ['Beginner', 'Intermediate', 'Advanced', 'Expert', 'Elite'];
export const MOVEMENT_CATEGORIES = ['Strength', 'Cardio', 'Gymnastics', 'Olympic Lifting', 'Conditioning'];
export const BODY_PARTS = ['Upper Body', 'Lower Body', 'Core', 'Full Body'];
export const EQUIPMENT = ['Barbell', 'Dumbbell', 'Kettlebell', 'Bodyweight', 'Machine', 'Ab wheel', 'Medicine ball', 'Other'];
export const VIDEO_SOURCES = ['youtube', 'sweat.com', 'trainwell.com', 'other'];
export const CATEGORY_TYPES = ['body_part', 'equipment', 'expertise_level', 'movement_category', 'movement_pattern'];

interface ExercisesClientProps {
  initialExercises: ExerciseMovement[];
  totalExercises: number;
}

// Add filter reducer for complex filter state management
type FilterAction =
  | { type: 'SET_FILTER'; key: keyof Filters; value: string }
  | { type: 'CLEAR_FILTERS' }
  | { type: 'SET_ALL_FILTERS'; filters: Filters };

function filterReducer(state: Filters, action: FilterAction): Filters {
  switch (action.type) {
    case 'SET_FILTER':
      return { ...state, [action.key]: action.value };
    case 'CLEAR_FILTERS':
      return {
        body_part: 'all',
        equipment: 'all',
        expertise_level: 'all',
        movement_category: 'all',
        has_video: 'all',
        has_image: 'all',
        has_records: 'all',
        video_source: 'all',
      };
    case 'SET_ALL_FILTERS':
      return { ...action.filters };
    default:
      return state;
  }
}

export default function ExercisesClient({ totalExercises }: ExercisesClientProps) {
  const supabase = createClientComponentClient<Database>();
  const queryClient = useQueryClient();

  // Use reducer for filter state
  const [filters, dispatchFilters] = useReducer(filterReducer, {
    body_part: 'all',
    equipment: 'all',
    expertise_level: 'all',
    movement_category: 'all',
    has_video: 'all',
    has_image: 'all',
    has_records: 'all',
    video_source: 'all',
  });

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [sortConfig, setSortConfig] = useState<SortConfig>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState<ExerciseMovement | null>(null);
  const [currentVideoPreview, setCurrentVideoPreview] = useState<{url: string, source: string} | null>(null);
  const [isClientSearch, setIsClientSearch] = useState(true); // Default to client-side search

  // Fetch all exercises with React Query
  const { data: exercisesData, isLoading } = useQuery({
    queryKey: ['allExercises'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('exercise_movements')
        .select('*');

      if (error) throw error;
      return data || [];
    },
    // Cache for 1 hour - adjust as needed
    staleTime: 60 * 60 * 1000,
    // Keep in cache even when inactive
    gcTime: 24 * 60 * 60 * 1000,
  });

  // Separate query for searching when needed
  const { data: searchResults, isLoading: isSearching } = useQuery({
    queryKey: ['exerciseSearch', debouncedSearchTerm],
    queryFn: async () => {
      if (!debouncedSearchTerm || isClientSearch) return null;

      const { data, error } = await supabase
        .from('exercise_movements')
        .select('*')
        .textSearch('fts', debouncedSearchTerm, {
          type: 'websearch',
          config: 'english'
        });

      if (error) throw error;
      return data || [];
    },
    // Don't run if there's no search term or using client search
    enabled: !!debouncedSearchTerm && !isClientSearch,
  });

  // Fetch related data (categories, images, videos) with React Query
  const { data: relatedData } = useQuery({
    queryKey: ['exerciseRelatedData'],
    queryFn: async () => {
      const exercises = exercisesData || [];
      if (exercises.length === 0) return { categories: [], images: [], videos: [] };

      const exerciseIds = exercises.map(ex => ex.id);

      // Split into chunks to avoid URL length limits
      const chunkSize = 100;
      const chunks = [];
      for (let i = 0; i < exerciseIds.length; i += chunkSize) {
        chunks.push(exerciseIds.slice(i, i + chunkSize));
      }

      const allCategories = [];
      const allImages = [];
      const allVideos = [];

      // Fetch data for each chunk
      for (const chunk of chunks) {
        const [categoriesResponse, imagesResponse, videosResponse] = await Promise.all([
          supabase.from('exercise_categories').select('*').in('exercise_id', chunk),
          supabase.from('exercise_images').select('*').in('exercise_id', chunk),
          supabase.from('exercise_videos').select('*').in('exercise_id', chunk)
        ]);

        if (categoriesResponse.data) allCategories.push(...categoriesResponse.data);
        if (imagesResponse.data) allImages.push(...imagesResponse.data);
        if (videosResponse.data) allVideos.push(...videosResponse.data);
      }

      return {
        categories: allCategories,
        images: allImages,
        videos: allVideos
      };
    },
    enabled: !!exercisesData && exercisesData.length > 0,
  });

  // Fetch exercises with records
  const { data: exerciseRecords } = useQuery({
    queryKey: ['exerciseRecords'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('exercise_records')
        .select('exercise_id')
        .not('exercise_id', 'is', null);

      if (error) throw error;

      // Create a Set of exercise IDs with records
      return new Set(data.map(record => record.exercise_id));
    },
  });

  // Handle search term change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Combine all data
  const combinedExercises = useMemo(() => {
    if (!exercisesData) return [];

    // Use search results if available, otherwise use all exercises
    const baseExercises = (searchResults && !isClientSearch) ? searchResults : exercisesData;

    return baseExercises.map(exercise => {
      // Get categories for this exercise
      const categories = relatedData?.categories.filter(cat =>
        cat.exercise_id === exercise.id
      ) || [];

      // Get images for this exercise
      const images = relatedData?.images.filter(img =>
        img.exercise_id === exercise.id
      ) || [];

      // Get videos for this exercise
      const videos = relatedData?.videos.filter(vid =>
        vid.exercise_id === exercise.id
      ) || [];

      // Check if has records
      const hasRecords = exerciseRecords?.has(exercise.id) || false;

      return {
        ...exercise,
        categories,
        images,
        videos,
        hasRecords
      };
    });
  }, [exercisesData, searchResults, relatedData, exerciseRecords, isClientSearch]);

  // Client-side filtering and search
  const filteredExercises = useMemo(() => {
    let result = [...combinedExercises];

    // Apply client-side search if enabled
    if (debouncedSearchTerm && isClientSearch) {
      const lowerSearch = debouncedSearchTerm.toLowerCase();
      result = result.filter(exercise => {
        // Search in name
        if (exercise.exercise_name.toLowerCase().includes(lowerSearch)) return true;

        // Search in description
        if (exercise.description && exercise.description.toLowerCase().includes(lowerSearch)) return true;

        // Search in categories
        if (exercise.categories?.some(cat =>
          cat.category_value.toLowerCase().includes(lowerSearch)
        )) return true;

        return false;
      });
    }

    // Apply filters
    if (filters.body_part !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'body_part' && cat.category_value === filters.body_part
        )
      );
    }

    if (filters.equipment !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'equipment' && cat.category_value === filters.equipment
        )
      );
    }

    if (filters.expertise_level !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'expertise_level' && cat.category_value === filters.expertise_level
        )
      );
    }

    if (filters.movement_category !== 'all') {
      result = result.filter(exercise =>
        exercise.categories?.some(cat =>
          cat.category_type === 'movement_category' && cat.category_value === filters.movement_category
        )
      );
    }

    if (filters.has_video !== 'all') {
      result = result.filter(exercise =>
        filters.has_video === 'yes'
          ? (exercise.videos && exercise.videos.length > 0)
          : (!exercise.videos || exercise.videos.length === 0)
      );
    }

    if (filters.has_image !== 'all') {
      result = result.filter(exercise =>
        filters.has_image === 'yes'
          ? (exercise.images && exercise.images.length > 0)
          : (!exercise.images || exercise.images.length === 0)
      );
    }

    if (filters.has_records !== 'all') {
      result = result.filter(exercise =>
        filters.has_records === 'yes' ? exercise.hasRecords : !exercise.hasRecords
      );
    }

    if (filters.video_source !== 'all') {
      result = result.filter(exercise =>
        exercise.videos?.some(video =>
          video.video_source === filters.video_source
        )
      );
    }

    return result;
  }, [combinedExercises, filters, debouncedSearchTerm, isClientSearch]);

  // Handle edit exercise
  const handleEdit = (exercise: ExerciseMovement) => {
    setSelectedExercise(exercise);
    setIsModalOpen(true);
  };

  // Handle delete exercise
  const handleDelete = async (id: string) => {
    try {
      // Check if exercise has records
      const { data: recordsData, error: recordsError } = await supabase
        .from('exercise_records')
        .select('id')
        .eq('exercise_id', id)
        .limit(1);

      if (recordsError) throw recordsError;

      if (recordsData && recordsData.length > 0) {
        alert('Cannot delete exercise with associated records');
        return;
      }

      // Delete related data first
      await Promise.all([
        supabase.from('exercise_videos').delete().eq('exercise_id', id),
        supabase.from('exercise_images').delete().eq('exercise_id', id),
        supabase.from('exercise_categories').delete().eq('exercise_id', id)
      ]);

      // Then delete the exercise
      const { error } = await supabase.from('exercise_movements').delete().eq('id', id);

      if (error) throw error;

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['allExercises'] });
      queryClient.invalidateQueries({ queryKey: ['exerciseRelatedData'] });
      queryClient.invalidateQueries({ queryKey: ['exerciseRecords'] });

    } catch (error) {
      console.error('Error deleting exercise:', error);
      alert('Error deleting exercise. Please try again.');
    }
  };

  // Handle form submission (create/update exercise)
  const handleSaveExercise = async (exercise: ExerciseMovement) => {
    try {
      const isNew = !exercise.id;

      if (isNew) {
        // Create new exercise
        const { data, error } = await supabase
          .from('exercise_movements')
          .insert({
            exercise_name: exercise.exercise_name,
            description: exercise.description,
            // Add required fields with default values
            body_part: '',
            equipment: '',
            expertise_level: '',
            movement_category: ''
          })
          .select();

        if (error) throw error;
        if (!data || data.length === 0) throw new Error('Failed to create exercise');

        const newExerciseId = data[0].id;

        // Save related data
        await saveRelatedData(exercise, newExerciseId);
      } else {
        // Update existing exercise
        const { error } = await supabase
          .from('exercise_movements')
          .update({
            exercise_name: exercise.exercise_name,
            description: exercise.description
          })
          .eq('id', exercise.id);

        if (error) throw error;

        // Save related data
        await saveRelatedData(exercise, exercise.id);
      }

      // Close modal and reset selected exercise
      setIsModalOpen(false);
      setSelectedExercise(null);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['allExercises'] });
      queryClient.invalidateQueries({ queryKey: ['exerciseRelatedData'] });

      if (debouncedSearchTerm && !isClientSearch) {
        queryClient.invalidateQueries({ queryKey: ['exerciseSearch', debouncedSearchTerm] });
      }
    } catch (error) {
      console.error('Error saving exercise:', error);
      alert('Error saving exercise. Please try again.');
    }
  };

  // Save related data (videos, images, categories)
  const saveRelatedData = async (exercise: ExerciseMovement, exerciseId: string) => {
    try {
      // First, delete existing related data
      await Promise.all([
        supabase.from('exercise_videos').delete().eq('exercise_id', exerciseId),
        supabase.from('exercise_images').delete().eq('exercise_id', exerciseId),
        supabase.from('exercise_categories').delete().eq('exercise_id', exerciseId)
      ]);

      // Then insert new data
      const videos = exercise.videos || [];
      if (videos.length > 0) {
        await supabase.from('exercise_videos').insert(
          videos.map(video => ({
            ...video,
            exercise_id: exerciseId
          }))
        );
      }

      const images = exercise.images || [];
      if (images.length > 0) {
        await supabase.from('exercise_images').insert(
          images.map(image => ({
            ...image,
            exercise_id: exerciseId
          }))
        );
      }

      const categories = exercise.categories || [];
      if (categories.length > 0) {
        await supabase.from('exercise_categories').insert(
          categories.map(category => ({
            ...category,
            exercise_id: exerciseId
          }))
        );
      }
    } catch (error) {
      console.error('Error saving related data:', error);
      throw error;
    }
  };

  // Open video preview dialog
  const handleOpenVideoPreview = (url: string, source: string = '') => {
    if (!url) return;
    setCurrentVideoPreview({ url, source });
  };

  // Close video preview dialog
  const handleCloseVideoPreview = () => {
    setCurrentVideoPreview(null);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    dispatchFilters({ type: 'CLEAR_FILTERS' });
  };

  // Toggle search mode function is defined but not currently used in the UI

  // Apply sorting
  const sortedExercises = useMemo(() => {
    if (!sortConfig) return filteredExercises;

    return [...filteredExercises].sort((a, b) => {
      const getSortValue = (exercise: ExerciseMovement, key: string) => {
        if (key === 'exercise_name') return exercise.exercise_name;

        // Get value from primary category
        if (['body_part', 'equipment', 'expertise_level', 'movement_category'].includes(key)) {
          return exercise.categories?.find(cat =>
            cat.category_type === key && cat.is_primary
          )?.category_value || '';
        }

        return '';
      };

      const aValue = getSortValue(a, sortConfig.key);
      const bValue = getSortValue(b, sortConfig.key);

      if (!aValue) return 1;
      if (!bValue) return -1;

      const comparison = aValue.localeCompare(bValue);
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [filteredExercises, sortConfig]);

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    return {
      totalPages: 1, // Single page since we're loading all at once
      totalFilteredItems: sortedExercises.length,
      totalSearchResults: totalExercises
    };
  }, [sortedExercises.length, totalExercises]);

  // Handle sorting
  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        if (current.direction === 'asc') {
          return { key, direction: 'desc' };
        }
        return null;
      }
      return { key, direction: 'asc' };
    });
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Exercise Movements</h1>
          <p className="text-sm text-gray-500">
            {debouncedSearchTerm
              ? `Found ${paginationInfo.totalSearchResults} exercises matching &quot;${debouncedSearchTerm}&quot;`
              : `Total: ${totalExercises} exercises`}
          </p>
        </div>
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedExercise(null)}>Add New Exercise</Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedExercise ? 'Edit Exercise' : 'Add New Exercise'}
              </DialogTitle>
            </DialogHeader>
            <ExerciseForm
              exercise={selectedExercise}
              onSave={(exercise: ExerciseMovement) => handleSaveExercise(exercise)}
              onCancel={() => setIsModalOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search Bar - Now prominently placed outside and above filters */}
      <Card className="p-4 mb-4">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="flex-grow w-full md:w-auto">
            <SearchBar
              onSearch={handleSearchChange}
              initialValue={searchTerm}
              isSearching={isSearching}
              placeholder="Search exercises by name, description, or category..."
            />
          </div>
          <Button
            variant="outline"
            onClick={clearFilters}
            disabled={isSearching}
            className="whitespace-nowrap"
          >
            Clear Search
          </Button>
        </div>
        {debouncedSearchTerm && (
          <div className="mt-2 text-sm text-gray-500">
            <p>
              Searching through exercise names, descriptions, and categories.
              Showing {paginationInfo.totalFilteredItems} of {paginationInfo.totalSearchResults} results.
            </p>
          </div>
        )}
      </Card>

      {/* Debug info (you can remove in production) */}
      <div className="mt-4 p-4 bg-gray-50 rounded mb-4">
        <h3 className="font-bold">Debug Info:</h3>
        <div>Search term: &quot;{debouncedSearchTerm}&quot;</div>
        <div>Total exercises: {combinedExercises.length}</div>
        <div>Filtered exercises: {sortedExercises.length}</div>
        <div>Is searching: {isSearching ? 'Yes' : 'No'}</div>
        <div>Client search enabled: {isClientSearch ? 'Yes' : 'No'}</div>
        <div>Loading state: {isLoading ? 'Loading' : 'Ready'}</div>
        <div className="mt-2">
          <a href="/debug/image-test" target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
            Open Image Debug Tool
          </a>
        </div>
      </div>

      {/* Filters */}
      <ExerciseFilters
        searchTerm={searchTerm}
        filters={filters}
        onSearchChange={handleSearchChange}
        onFilterChange={(newFilters) => {
          Object.entries(newFilters).forEach(([key, value]) => {
            dispatchFilters({
              type: 'SET_FILTER',
              key: key as keyof Filters,
              value
            });
          });
        }}
        onClearFilters={clearFilters}
      />

      {/* Table */}
      <ExerciseTable
        exercises={sortedExercises}
        sortConfig={sortConfig}
        currentPage={1}
        pageSize={sortedExercises.length}
        totalPages={paginationInfo.totalPages}
        totalExercises={paginationInfo.totalFilteredItems}
        totalAllExercises={paginationInfo.totalSearchResults}
        onSort={handleSort}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onOpenVideoPreview={handleOpenVideoPreview}
        onPageChange={() => {}} // No-op since we're using infinite scroll
        onPageSizeChange={() => {}}
        isLoading={isLoading || isSearching}
      />

      {/* Toggle search mode button */}
      <div className="mt-6 text-center">
        <Button
          onClick={() => setIsClientSearch(prev => !prev)}
          variant="outline"
          className="mx-2"
        >
          {isClientSearch ? 'Switch to Server Search' : 'Switch to Client Search'}
        </Button>
      </div>

      {/* Media Preview Dialog */}
      {currentVideoPreview && (
        <MediaPreviewDialog
          mediaItem={currentVideoPreview}
          onClose={handleCloseVideoPreview}
        />
      )}
    </div>
  );
}