// SearchBar.tsx (new component)
'use client'

import { useState, useEffect } from 'react'
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import { useDebounce } from '@/hooks/useDebounce'

interface SearchBarProps {
  onSearch: (value: string) => void
  initialValue?: string
  isSearching?: boolean
  placeholder?: string
}

export default function SearchBar({ 
  onSearch, 
  initialValue = '', 
  isSearching = false,
  placeholder = 'Search exercises...'
}: SearchBarProps) {
  const [searchInput, setSearchInput] = useState(initialValue)
  const debouncedSearch = useDebounce(searchInput, 500)
  
  useEffect(() => {
    onSearch(debouncedSearch)
  }, [debouncedSearch, onSearch])

  return (
    <div className="relative w-full">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>
      <Input
        type="text"
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
        placeholder={placeholder}
        className="pl-10 w-full"
        disabled={isSearching}
      />
      {isSearching && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
        </div>
      )}
    </div>
  )
}