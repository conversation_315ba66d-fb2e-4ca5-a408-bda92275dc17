// components/invoices/InvoiceActionsDropdown.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Eye, 
  Send, 
  X, 
  CheckCircle, 
  Download,
  QrCode,
  AlertCircle,
  RefreshCw,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface InvoiceActionsDropdownProps {
  invoice: Invoice;
  onActionComplete?: () => void;
  onShowChecker?: (invoice: Invoice) => void;
  onShowAddLine?: (invoice: Invoice) => void;
  onShowAddPayment?: (invoice: Invoice) => void;
}

export default function InvoiceActionsDropdown({
  invoice,
  onActionComplete,
  onShowChecker,
  onShowAddLine,
  onShowAddPayment
}: InvoiceActionsDropdownProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleSubmitToMyData = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/mydata/send-invoice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ invoiceId: invoice.id }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to submit invoice');
      }

      toast.success('Invoice submitted to myDATA successfully');
      onActionComplete?.();
    } catch (error) {
      console.error('Error submitting invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit invoice');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelInvoice = async () => {
    if (!confirm(
      `Are you sure you want to cancel invoice ${invoice.invoice_series}-${invoice.invoice_number} in myDATA?\n\n` +
      'This will cancel the invoice in the AADE myDATA system. This action cannot be undone.'
    )) {
      return;
    }

    setIsCanceling(true);
    try {
      const response = await fetch('/api/mydata/cancel-invoice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ invoiceId: invoice.id }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to cancel invoice');
      }

      toast.success('Invoice canceled in myDATA successfully');
      onActionComplete?.();
    } catch (error) {
      console.error('Error canceling invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to cancel invoice');
    } finally {
      setIsCanceling(false);
    }
  };

  const handleDeleteInvoice = async () => {
    if (!confirm(
      `Are you sure you want to delete invoice ${invoice.invoice_series}-${invoice.invoice_number}?\n\n` +
      'This will permanently remove the invoice and all related data from the database. This action cannot be undone.'
    )) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch('/api/mydata/delete-invoice', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ invoiceId: invoice.id }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to delete invoice');
      }

      toast.success('Invoice deleted successfully');
      onActionComplete?.();
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete invoice');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleGeneratePdf = async () => {
    setIsGeneratingPdf(true);
    try {
      const response = await fetch('/api/mydata/generate-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ invoiceId: invoice.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PDF');
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `invoice_${invoice.invoice_series}-${invoice.invoice_number}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('PDF generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate PDF');
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleViewDetails = () => {
    router.push(`/admin/invoices/${invoice.id}`);
  };

  const handleViewQrCode = () => {
    if (invoice.qr_url) {
      window.open(invoice.qr_url, '_blank');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'pending': return 'text-blue-600';
      case 'rate_limited': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  // Determine what actions are available based on invoice status
  const canSubmit = invoice.status === 'draft' || invoice.status === 'error';
  const canCancel = invoice.status === 'submitted' && invoice.mark;
  const canResubmit = invoice.status === 'error' || invoice.status === 'rate_limited';
  const canDelete = invoice.status === 'draft' || invoice.status === 'error' || invoice.status === 'canceled';
  const hasQrCode = invoice.status === 'submitted' && invoice.qr_url;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {/* Always available actions */}
        <DropdownMenuItem onClick={handleViewDetails}>
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => onShowChecker?.(invoice)}>
          <CheckCircle className="mr-2 h-4 w-4" />
          Check Readiness
        </DropdownMenuItem>

        <DropdownMenuItem 
          onClick={handleGeneratePdf}
          disabled={isGeneratingPdf}
        >
          <Download className="mr-2 h-4 w-4" />
          {isGeneratingPdf ? 'Generating...' : 'Generate PDF'}
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Status-specific actions */}
        {canSubmit && (
          <DropdownMenuItem 
            onClick={handleSubmitToMyData}
            disabled={isSubmitting}
          >
            <Send className="mr-2 h-4 w-4" />
            {isSubmitting ? 'Submitting...' : 'Submit to myDATA'}
          </DropdownMenuItem>
        )}

        {canResubmit && (
          <DropdownMenuItem 
            onClick={handleSubmitToMyData}
            disabled={isSubmitting}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {isSubmitting ? 'Resubmitting...' : 'Resubmit to myDATA'}
          </DropdownMenuItem>
        )}

        {canCancel && (
          <DropdownMenuItem 
            onClick={handleCancelInvoice}
            disabled={isCanceling}
            className="text-red-600 focus:text-red-600"
          >
            <X className="mr-2 h-4 w-4" />
            {isCanceling ? 'Canceling...' : 'Cancel in myDATA'}
          </DropdownMenuItem>
        )}

        {canDelete && (
          <DropdownMenuItem 
            onClick={handleDeleteInvoice}
            disabled={isDeleting}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? 'Deleting...' : 'Delete Invoice'}
          </DropdownMenuItem>
        )}

        {hasQrCode && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleViewQrCode}>
              <QrCode className="mr-2 h-4 w-4" />
              View QR Code
            </DropdownMenuItem>
          </>
        )}

        {/* Quick edit actions for draft invoices */}
        {invoice.status === 'draft' && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onShowAddLine?.(invoice)}>
              <AlertCircle className="mr-2 h-4 w-4" />
              Add Line Item
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onShowAddPayment?.(invoice)}>
              <AlertCircle className="mr-2 h-4 w-4" />
              Add Payment Method
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}