// components/mydata/InvoiceActions.tsx
"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Send, Download, FileText } from 'lucide-react';
import { toast } from 'sonner';
import type { Database } from '@/types/supabase';

// Simple spinner component if you don't have one
function Spinner({ className = "" }: { className?: string }) {
  return (
    <svg 
      className={`animate-spin h-4 w-4 ${className}`} 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
}

interface InvoiceActionsProps {
  invoiceId: string;
  invoiceStatus: string;
  hasMyDataMark: boolean;
  disabled?: boolean;
  onSuccess?: () => void;
}

export default function InvoiceActions({ 
  invoiceId, 
  invoiceStatus, 
  hasMyDataMark,
  disabled = false, 
  onSuccess 
}: InvoiceActionsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Create a client-side Supabase client
  const supabase = createClientComponentClient<Database>();

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);
      
      // Check for valid auth session first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        throw new Error('Authentication required. Please log in again.');
      }
      
      // Log the payload for debugging
      const payload = { invoiceId };
      console.log('payload', payload);
      
      // Make the API call
      const response = await fetch('/api/mydata/send-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'same-origin'
      });
      
      // Get the response data
      const data = await response.json();
      
      // Handle non-success responses
      if (!response.ok) {
        throw new Error(data.error || data.message || 'Error submitting invoice');
      }
      
      // Handle success
      setSuccess('Invoice successfully submitted to myDATA!');
      toast.success('Invoice submitted to myDATA successfully');
      if (onSuccess) {
        onSuccess();
      }
      
    } catch (err) {
      console.error('Error submitting invoice:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit invoice';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGeneratePdf = async () => {
    try {
      setIsGeneratingPdf(true);
      
      // Check for valid auth session first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        throw new Error('Authentication required. Please log in again.');
      }
      
      console.log('Generating PDF for invoice:', invoiceId);
      
      // Make the API call
      const response = await fetch('/api/mydata/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invoiceId }),
        credentials: 'same-origin'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PDF');
      }

      // Get the filename from response headers or create a default one
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'invoice.pdf';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('PDF generated and downloaded successfully');
      
    } catch (err) {
      console.error('Error generating PDF:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate PDF';
      toast.error(errorMessage);
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  // Determine which actions are available based on invoice status
  const canSubmit = invoiceStatus === 'draft' || invoiceStatus === 'error';
  const canResubmit = invoiceStatus === 'error' || invoiceStatus === 'rate_limited';
  const isSubmitted = invoiceStatus === 'submitted';

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex flex-col sm:flex-row gap-3">
        {/* Submit/Resubmit Button */}
        {(canSubmit || canResubmit) && (
          <Button 
            onClick={handleSubmit} 
            disabled={disabled || isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? (
              <>
                <Spinner className="mr-2" />
                {canResubmit ? 'Resubmitting...' : 'Submitting...'}
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {canResubmit ? 'Resubmit to myDATA' : 'Submit to myDATA'}
              </>
            )}
          </Button>
        )}

        {/* Status indicator for submitted invoices */}
        {isSubmitted && hasMyDataMark && (
          <div className="flex-1 px-4 py-2 bg-green-50 border border-green-200 rounded-md text-center">
            <span className="text-green-700 font-medium">✓ Submitted to myDATA</span>
          </div>
        )}

        {/* PDF Generation Button */}
        <Button 
          onClick={handleGeneratePdf}
          disabled={isGeneratingPdf}
          variant="outline"
          className="flex-1 sm:flex-initial"
        >
          {isGeneratingPdf ? (
            <>
              <Spinner className="mr-2" />
              Generating...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Generate PDF
            </>
          )}
        </Button>
      </div>

      {/* Additional Info */}
      <div className="text-sm text-gray-600 space-y-1">
        <div className="flex items-center justify-between">
          <span>Status:</span>
          <span className={`font-medium ${
            isSubmitted ? 'text-green-600' : 
            invoiceStatus === 'error' ? 'text-red-600' : 
            invoiceStatus === 'pending' ? 'text-blue-600' :
            'text-gray-600'
          }`}>
            {invoiceStatus.charAt(0).toUpperCase() + invoiceStatus.slice(1)}
          </span>
        </div>
        
        {hasMyDataMark && (
          <div className="flex items-center justify-between">
            <span>myDATA:</span>
            <span className="text-green-600 font-medium">Registered</span>
          </div>
        )}
      </div>
    </div>
  );
}