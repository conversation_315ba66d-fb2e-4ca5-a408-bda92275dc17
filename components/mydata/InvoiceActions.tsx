// components/mydata/InvoiceActions.tsx
"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

// Simple spinner component if you don't have one
function Spinner({ className = "" }: { className?: string }) {
  return (
    <svg 
      className={`animate-spin h-4 w-4 ${className}`} 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
}

interface InvoiceActionsProps {
  invoiceId: string;
  disabled?: boolean;
  onSuccess?: () => void;
}

export default function InvoiceActions({ invoiceId, disabled = false, onSuccess }: InvoiceActionsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Create a client-side Supabase client
  const supabase = createClientComponentClient<Database>();

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);
      
      // Check for valid auth session first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        throw new Error('Authentication required. Please log in again.');
      }
      
      // Log the payload for debugging
      const payload = { invoiceId };
      console.log('payload', payload);
      
      // Make the API call
      const response = await fetch('/api/mydata/send-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'same-origin'
      });
      
      // Get the response data
      const data = await response.json();
      
      // Handle non-success responses
      if (!response.ok) {
        throw new Error(data.error || data.message || 'Error submitting invoice');
      }
      
      // Handle success
      setSuccess('Invoice successfully submitted to myDATA!');
      if (onSuccess) {
        onSuccess();
      }
      
    } catch (err) {
      console.error('Error submitting invoice:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit invoice');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      <Button 
        onClick={handleSubmit} 
        disabled={disabled || isSubmitting}
        className="w-full"
      >
        {isSubmitting ? (
          <>
            <Spinner className="mr-2" />
            Submitting to myDATA...
          </>
        ) : (
          'Submit to myDATA'
        )}
      </Button>
    </div>
  );
}