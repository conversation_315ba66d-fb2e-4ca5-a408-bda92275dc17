'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import AddInvoiceLineForm from './AddInvoiceLineForm';
import AddPaymentMethodForm from './AddPaymentMethodForm';

interface InvoiceCheckerProps {
  invoiceId: string;
  invoiceTotal: number;
  onReady?: () => void;
}

interface CheckResult {
  invoice: {
    id: string;
    series: string;
    number: string;
    status: string;
    hasLines: boolean;
    lineCount: number;
    hasPaymentMethods: boolean;
    paymentMethodCount: number;
    hasCompanySettings: boolean;
  };
  issues: string[];
}

export default function InvoiceChecker({
  invoiceId,
  invoiceTotal,
  onReady
}: InvoiceCheckerProps) {
  const [isChecking, setIsChecking] = useState(false);
  const [checkResult, setCheckResult] = useState<CheckResult | null>(null);
  const [showAddLine, setShowAddLine] = useState(false);
  const [showAddPayment, setShowAddPayment] = useState(false);

  const checkInvoice = async () => {
    setIsChecking(true);

    try {
      const response = await fetch(`/api/mydata/check-invoice?invoiceId=${invoiceId}`);

      if (!response.ok) {
        throw new Error('Failed to check invoice');
      }

      const data = await response.json();
      setCheckResult(data);

      // If there are no issues and onReady callback is provided, call it
      if (data.issues.length === 0 && onReady) {
        onReady();
      }
    } catch (error) {
      console.error('Error checking invoice:', error);
      toast.error('Failed to check invoice');
    } finally {
      setIsChecking(false);
    }
  };

  // Check invoice on component mount
  useEffect(() => {
    checkInvoice();
  }, [invoiceId, checkInvoice]);

  const handleAddLineSuccess = () => {
    setShowAddLine(false);
    checkInvoice();
  };

  const handleAddPaymentSuccess = () => {
    setShowAddPayment(false);
    checkInvoice();
  };

  if (isChecking && !checkResult) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <p className="ml-3 text-gray-500">Checking invoice...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!checkResult) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to check invoice. Please try again.
            </AlertDescription>
          </Alert>
          <Button onClick={checkInvoice} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const { invoice, issues } = checkResult;
  const isReady = issues.length === 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Invoice Readiness Check</CardTitle>
          <CardDescription>
            Check if this invoice is ready to be submitted to myDATA
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-md">
                <div className="flex items-center">
                  {invoice.hasLines ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <h3 className="font-medium">Line Items</h3>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {invoice.lineCount} line{invoice.lineCount !== 1 ? 's' : ''} found
                </p>
              </div>

              <div className="p-4 border rounded-md">
                <div className="flex items-center">
                  {invoice.hasPaymentMethods ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <h3 className="font-medium">Payment Methods</h3>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {invoice.paymentMethodCount} method{invoice.paymentMethodCount !== 1 ? 's' : ''} found
                </p>
              </div>

              <div className="p-4 border rounded-md">
                <div className="flex items-center">
                  {invoice.hasCompanySettings ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <h3 className="font-medium">Company Settings</h3>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {invoice.hasCompanySettings ? 'Configured' : 'Not configured'}
                </p>
              </div>
            </div>

            {issues.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Issues Found</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc pl-5 mt-2">
                    {issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {isReady && (
              <Alert variant="default" className="bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertTitle className="text-green-800">Ready to Submit</AlertTitle>
                <AlertDescription className="text-green-700">
                  This invoice is ready to be submitted to myDATA.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={checkInvoice} disabled={isChecking}>
            {isChecking ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </>
            )}
          </Button>

          <div className="space-x-2">
            {!invoice.hasLines && (
              <Button
                variant="secondary"
                onClick={() => setShowAddLine(!showAddLine)}
              >
                Add Line Item
              </Button>
            )}

            {!invoice.hasPaymentMethods && (
              <Button
                variant="secondary"
                onClick={() => setShowAddPayment(!showAddPayment)}
              >
                Add Payment Method
              </Button>
            )}

            {!invoice.hasCompanySettings && (
              <Button
                variant="secondary"
                onClick={() => window.location.href = '/admin/mydata/settings'}
              >
                Configure Settings
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>

      {showAddLine && (
        <AddInvoiceLineForm
          invoiceId={invoiceId}
          onSuccess={handleAddLineSuccess}
        />
      )}

      {showAddPayment && (
        <AddPaymentMethodForm
          invoiceId={invoiceId}
          invoiceTotal={invoiceTotal}
          onSuccess={handleAddPaymentSuccess}
        />
      )}
    </div>
  );
}
