// components/mydata/InvoiceSubmitButton.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Send, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface InvoiceSubmitButtonProps {
  invoiceId: string;
}

export default function InvoiceSubmitButton({ invoiceId }: InvoiceSubmitButtonProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/mydata/send-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invoiceId }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit invoice');
      }
      
      if (data.success) {
        toast.success('Invoice submitted to myDATA successfully');
        // Refresh the page to show updated status
        router.refresh();
      } else {
        throw new Error('Failed to submit invoice: ' + (data.errors?.[0]?.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error submitting invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit invoice');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Button
      onClick={handleSubmit}
      disabled={isSubmitting}
    >
      {isSubmitting ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Submitting...
        </>
      ) : (
        <>
          <Send className="h-4 w-4 mr-2" />
          Submit to myDATA
        </>
      )}
    </Button>
  );
}