// components/mydata/MyDataSettingsForm.tsx (modified)
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { createClient } from '@supabase/supabase-js';
import { toast } from 'react-hot-toast';
import type { Database } from '@/types/supabase';

type CompanySettings = Database['public']['Tables']['company_settings']['Row'];

export default function MyDataSettingsForm({
  companySettings,
  environmentInfo
}: {
  companySettings?: CompanySettings;
  environmentInfo: {
    environment: 'development' | 'production';
    hasCredentials: boolean;
  };
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const { handleSubmit } = useForm({
    defaultValues: {
      // Company settings
      companyName: companySettings?.companyName || '',
      vatNumber: companySettings?.vatNumber || '',
      country: companySettings?.country || 'GR',
      branch: companySettings?.branch || '0',
      address: companySettings?.address || '',
      postalCode: companySettings?.postalCode || '',
      city: companySettings?.city || '',
      defaultClassificationType: companySettings?.defaultClassificationType || 'E3_561_007',
      defaultClassificationCategory: companySettings?.defaultClassificationCategory || 'category1_3',
    }
  });

  const saveSettings = async (data: {
    companyName: string;
    vatNumber: string;
    country: string;
    branch: string;
    address: string;
    postalCode: string;
    city: string;
    defaultClassificationType: string;
    defaultClassificationCategory: string;
  }) => {
    setIsSubmitting(true);

    try {
      // Extract company settings
      const companyData = {
        companyName: data.companyName,
        vatNumber: data.vatNumber,
        country: data.country,
        branch: data.branch,
        address: data.address,
        postalCode: data.postalCode,
        city: data.city,
        defaultClassificationType: data.defaultClassificationType,
        defaultClassificationCategory: data.defaultClassificationCategory
      };

      // Save company settings
      if (companySettings?.id) {
        // Update existing settings
        await supabase
          .from('company_settings')
          .update(companyData)
          .eq('id', companySettings.id);
      } else {
        // Insert new settings
        await supabase
          .from('company_settings')
          .insert([companyData]);
      }

      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSubmitting(false);
    }
  };

  const testConnection = async () => {
    setIsTestingConnection(true);

    try {
      const response = await fetch('/api/mydata/test-connection', {
        method: 'POST'
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Connection successful!');
      } else {
        toast.error(`Connection failed: ${data.error}`);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error('Failed to test connection');
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(saveSettings)} className="space-y-6">
      {/* Company Settings */}
      <div className="space-y-4 p-4 border rounded-md">
        <h2 className="text-lg font-semibold">Company Settings</h2>

        {/* Same company settings fields as before */}
        {/* ... */}
      </div>

      {/* API Credentials */}
      <div className="space-y-4 p-4 border rounded-md">
        <h2 className="text-lg font-semibold">myDATA API Credentials</h2>

        <div className="bg-gray-50 p-4 rounded-md">
          <p className="text-sm text-gray-700">
            API credentials are configured using environment variables:
          </p>

          <div className="mt-2">
            <p className="text-sm font-medium text-gray-700">
              Environment: <span className="font-bold">{environmentInfo.environment}</span>
            </p>
            <p className="text-sm font-medium text-gray-700">
              Credentials Status: {' '}
              <span className={`font-bold ${environmentInfo.hasCredentials ? 'text-green-600' : 'text-red-600'}`}>
                {environmentInfo.hasCredentials ? 'Configured' : 'Missing'}
              </span>
            </p>
          </div>

          {!environmentInfo.hasCredentials && (
            <div className="mt-2 text-sm text-red-600">
              Please configure the following environment variables:
              <ul className="list-disc list-inside mt-1">
                <li>{environmentInfo.environment === 'production' ? 'MYDATA_USERNAME_PROD' : 'MYDATA_USERNAME_DEV'}</li>
                <li>{environmentInfo.environment === 'production' ? 'MYDATA_SUBSCRIPTION_KEY_PROD' : 'MYDATA_SUBSCRIPTION_KEY_DEV'}</li>
              </ul>
            </div>
          )}

          <div className="mt-4">
            <button
              type="button"
              onClick={testConnection}
              disabled={isTestingConnection || !environmentInfo.hasCredentials}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isTestingConnection ? 'Testing...' : 'Test Connection'}
            </button>
          </div>
        </div>
      </div>

      {/* Environment */}
      <div className="space-y-4 p-4 border rounded-md">
        <h2 className="text-lg font-semibold">Environment</h2>

        <p className="text-sm text-gray-700">
          The system will automatically use the development environment (mydataapidev.aade.gr)
          when running in development mode, and the production environment (mydatapi.aade.gr)
          when running in production mode.
        </p>

        <p className="text-sm text-gray-700 font-medium">
          Current environment: {environmentInfo.environment}
        </p>
      </div>

      {/* Submit */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </form>
  );
}