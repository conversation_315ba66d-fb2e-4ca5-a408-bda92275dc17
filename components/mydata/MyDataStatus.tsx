// components/mydata/MyDataStatus.tsx
'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Server,
  RefreshCw,
  Loader2,
  Settings,
  ExternalLink,
  Key
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Link from 'next/link';

interface MyDataStatusProps {
  environment: 'development' | 'production';
  hasCredentials: boolean;
  className?: string;
}

export default function MyDataStatus({
  environment,
  hasCredentials,
  className = ''
}: MyDataStatusProps) {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'error'>('unknown');
  const [connectionMessage, setConnectionMessage] = useState<string | null>(null);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  // Define testConnection function with useCallback
  const testConnection = useCallback(async () => {
    if (!hasCredentials) {
      setConnectionStatus('error');
      setConnectionMessage('Missing myDATA API credentials');
      return;
    }

    setIsTestingConnection(true);
    setConnectionStatus('unknown');
    setConnectionMessage(null);

    try {
      const response = await fetch('/api/mydata/test-connection', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setConnectionStatus('success');
        setConnectionMessage('Connection successful');
      } else {
        setConnectionStatus('error');
        setConnectionMessage(data.error || 'Failed to connect to myDATA API');
      }
    } catch (error) {
      setConnectionStatus('error');
      setConnectionMessage(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsTestingConnection(false);
      setLastChecked(new Date());
    }
  }, [hasCredentials]);

  // Check connection status on component mount
  useEffect(() => {
    if (hasCredentials) {
      testConnection();
    }
  }, [hasCredentials, testConnection]);

  const getStatusContent = () => {
    // If credentials are missing
    if (!hasCredentials) {
      return {
        icon: <Key className="h-8 w-8 text-amber-500" />,
        title: 'Missing Credentials',
        description: 'myDATA API credentials are not configured.',
        variant: 'warning' as const,
        progress: 0,
      };
    }

    // Based on connection status
    switch (connectionStatus) {
      case 'success':
        return {
          icon: <CheckCircle2 className="h-8 w-8 text-green-500" />,
          title: 'Connected',
          description: 'Successfully connected to myDATA API.',
          variant: 'success' as const,
          progress: 100,
        };
      case 'error':
        return {
          icon: <XCircle className="h-8 w-8 text-red-500" />,
          title: 'Connection Error',
          description: connectionMessage || 'Failed to connect to myDATA API.',
          variant: 'destructive' as const,
          progress: 30,
        };
      default:
        return {
          icon: <AlertTriangle className="h-8 w-8 text-amber-500" />,
          title: 'Unknown Status',
          description: 'Connection status has not been tested.',
          variant: 'outline' as const,
          progress: 50,
        };
    }
  };

  const status = getStatusContent();
  const apiUrl = environment === 'production'
    ? 'https://mydatapi.aade.gr/myDATA'
    : 'https://mydataapidev.aade.gr';

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg flex items-center">
              <Server className="h-5 w-5 mr-2" />
              myDATA API Status
            </CardTitle>
            <CardDescription>
              {environment === 'production'
                ? 'Connected to production environment'
                : 'Connected to development environment'}
            </CardDescription>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant={environment === 'production' ? 'default' : 'secondary'}>
                  {environment}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>{environment === 'production'
                  ? 'Using production API endpoint'
                  : 'Using development/test API endpoint'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="flex items-center space-x-4 mb-4">
          {status.icon}
          <div className="flex-1">
            <h3 className="font-medium">{status.title}</h3>
            <p className="text-sm text-gray-500">{status.description}</p>
          </div>
        </div>

        <Progress value={status.progress} className="h-2" />

        <div className="mt-4 space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">API Endpoint:</span>
            <span className="font-mono text-xs">{apiUrl}</span>
          </div>

          {lastChecked && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Last Checked:</span>
              <span>{lastChecked.toLocaleTimeString()}</span>
            </div>
          )}

          {!hasCredentials && (
            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-amber-800">Missing Credentials</h4>
                  <div className="mt-1 text-sm text-amber-700">
                    <p>
                      myDATA API credentials are not configured. Add the following environment variables:
                    </p>
                    <ul className="mt-1 list-disc list-inside text-xs font-mono">
                      {environment === 'production' ? (
                        <>
                          <li>MYDATA_USERNAME_PROD</li>
                          <li>MYDATA_SUBSCRIPTION_KEY_PROD</li>
                        </>
                      ) : (
                        <>
                          <li>MYDATA_USERNAME_DEV</li>
                          <li>MYDATA_SUBSCRIPTION_KEY_DEV</li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-2 flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={testConnection}
          disabled={isTestingConnection || !hasCredentials}
        >
          {isTestingConnection ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Connection
            </>
          )}
        </Button>

        <div className="flex space-x-2">
          <Link href="/admin/mydata/settings">
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>

          <a
            href={environment === 'production'
              ? 'https://www.aade.gr/mydata'
              : 'https://mydata-dev-register.azurewebsites.net'}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button variant="ghost" size="sm">
              <ExternalLink className="h-4 w-4 mr-2" />
              {environment === 'production' ? 'AADE Portal' : 'Test Portal'}
            </Button>
          </a>
        </div>
      </CardFooter>
    </Card>
  );
}