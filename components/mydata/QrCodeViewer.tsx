// components/mydata/QrCodeViewer.tsx
'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Loader2, QrCode, ExternalLink, Download } from 'lucide-react';

interface QrCodeViewerProps {
  qrUrl: string;
  mark: string;
  className?: string;
}

export default function QrCodeViewer({ qrUrl, mark, className = '' }: QrCodeViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [qrImageUrl, setQrImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showFullscreen, setShowFullscreen] = useState(false);

  useEffect(() => {
    const loadQrCode = async () => {
      if (!qrUrl) {
        setError('No QR code URL available');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // If the QR URL is already an image URL, we can use it directly
        if (qrUrl.match(/\.(jpg|jpeg|png|gif|svg)$/i)) {
          setQrImageUrl(qrUrl);
        } else {
          // Otherwise, we need to generate a QR code from the URL
          // Using a QR code generator library or service
          // For simplicity, we'll use a public API here
          const encodedUrl = encodeURIComponent(qrUrl);
          const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
          setQrImageUrl(qrImageUrl);
        }
      } catch (err) {
        console.error('Error loading QR code:', err);
        setError('Failed to load QR code');
      } finally {
        setIsLoading(false);
      }
    };

    loadQrCode();
  }, [qrUrl]);

  const handleDownloadQrCode = () => {
    if (!qrImageUrl) return;

    // Create an anchor element and trigger download
    const link = document.createElement('a');
    link.href = qrImageUrl;
    link.download = `mydata-qr-${mark}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2 flex items-center">
          <QrCode className="h-5 w-5 mr-2" />
          myDATA QR Code
        </h3>

        <Tabs defaultValue="view" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view">View QR</TabsTrigger>
            <TabsTrigger value="info">Information</TabsTrigger>
          </TabsList>

          <TabsContent value="view" className="py-4">
            <div className="flex flex-col items-center">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center h-48">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
                  <span className="text-sm text-gray-500">Loading QR code...</span>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center h-48">
                  <span className="text-sm text-red-500 mb-2">{error}</span>
                  <Button variant="outline" size="sm" asChild>
                    <a href={qrUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open URL Directly
                    </a>
                  </Button>
                </div>
              ) : (
                <>
                  <div className="relative h-48 w-48 mb-4 border rounded-md overflow-hidden">
                    {qrImageUrl && (
                      <Image
                        src={qrImageUrl}
                        alt="myDATA QR Code"
                        fill
                        sizes="(max-width: 768px) 100vw, 200px"
                        className="object-contain"
                      />
                    )}
                  </div>

                  <div className="flex justify-center space-x-3">
                    <Dialog open={showFullscreen} onOpenChange={setShowFullscreen}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <QrCode className="h-4 w-4 mr-2" />
                          Fullscreen
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg">
                        <DialogHeader>
                          <DialogTitle>myDATA QR Code - MARK: {mark}</DialogTitle>
                        </DialogHeader>
                        <div className="flex flex-col items-center p-4">
                          {qrImageUrl && (
                            <div className="relative h-80 w-80 mb-4">
                              <Image
                                src={qrImageUrl}
                                alt="myDATA QR Code"
                                fill
                                sizes="(max-width: 768px) 100vw, 320px"
                                className="object-contain"
                              />
                            </div>
                          )}
                        </div>
                      </DialogContent>
                    </Dialog>

                    <Button variant="outline" size="sm" onClick={handleDownloadQrCode}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>

                    <Button variant="outline" size="sm" asChild>
                      <a href={qrUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Online
                      </a>
                    </Button>
                  </div>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="info" className="py-4">
            <div className="text-sm space-y-3">
              <p>
                The myDATA QR code is a digital signature that verifies the invoice
                has been properly registered in the AADE myDATA system.
              </p>

              <p>
                <strong>This QR code contains:</strong>
              </p>

              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>The invoice&apos;s unique MARK number: <span className="font-mono">{mark}</span></li>
                <li>Information about the issuer and recipient</li>
                <li>Date of issue and invoice details</li>
                <li>Total amount information</li>
              </ul>

              <p>
                Scanning this QR code allows anyone to verify the legitimacy of
                this invoice through the AADE system.
              </p>

              <p className="text-xs text-gray-500 mt-4">
                Note: This QR code should be included on printed copies of the invoice.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
}