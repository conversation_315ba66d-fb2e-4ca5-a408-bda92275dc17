'use client'

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from 'lucide-react';
import { Switch } from "@/components/ui/switch";

type Wod = Database['public']['Tables']['wod']['Row'];
type ExerciseMovement = Database['public']['Tables']['exercise_movements']['Row'];

interface WodFormProps {
  wod?: Wod;
  exercises: ExerciseMovement[];
}

export default function WodForm({ wod, exercises }: WodFormProps) {
  const router = useRouter();
  const supabase = createClientComponentClient<Database>();
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<{
    date: string;
    content: string;
    warmup: string;
    isPublished: boolean;
    exercises: string[];
    published_at?: string | null;
  }>({
    date: wod?.date || new Date().toISOString().split('T')[0],
    content: wod?.content || '',
    warmup: wod?.warmup || '',
    isPublished: wod?.is_published || false,
    exercises: wod?.exercises || [],
    published_at: wod?.published_at || null
  });
  const [alphabetFilter, setAlphabetFilter] = useState<string | null>(null);

  const filteredExercises = exercises.filter(exercise => {
    // First apply the search term filter
    const matchesSearch = exercise.exercise_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Then apply the alphabet filter if it's set
    const matchesAlphabet = !alphabetFilter || 
      exercise.exercise_name.toUpperCase().startsWith(alphabetFilter);
    
    return matchesSearch && matchesAlphabet;
  });

  // Sort exercises alphabetically to ensure consistent order
  const sortedFilteredExercises = filteredExercises.sort((a, b) => 
    a.exercise_name.localeCompare(b.exercise_name)
  );

  // Function to scroll to a specific letter
  const scrollToLetter = (letter: string) => {
    const target = sortedFilteredExercises.find(ex => 
      ex.exercise_name.toUpperCase().startsWith(letter)
    );
    
    if (target && scrollContainerRef.current) {
      // Find the index of the target exercise
      const index = sortedFilteredExercises.indexOf(target);
      
      // Calculate approximate scroll position (each item ~40px height)
      const scrollTop = index * 40;
      scrollContainerRef.current.scrollTop = scrollTop;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const wodData = {
        date: formData.date,
        content: formData.content,
        warmup: formData.warmup,
        is_published: formData.isPublished,
        exercises: formData.exercises,
        published_at: formData.published_at
      };

      if (wod) {
        // Update
        await supabase
          .from('wod')
          .update(wodData)
          .eq('id', wod.id);
      } else {
        // Create
        await supabase
          .from('wod')
          .insert([wodData]);
      }
      
      router.push('/admin/wods');
      router.refresh();
    } catch (error) {
      console.error('Error saving WOD:', error);
    }
  };

  const handleAlphabetClick = (e: React.MouseEvent, letter: string | null) => {
    e.preventDefault(); // Prevent form submission
    setAlphabetFilter(letter);
  };

  return (
    <div className="container mx-auto py-10">
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">
            {wod ? 'Edit WOD' : 'Create New WOD'}
          </h1>
          <div className="flex items-center space-x-2">
            <Switch
              id="published"
              checked={formData.isPublished}
              onCheckedChange={(checked) => setFormData(prev => ({ 
                ...prev, 
                isPublished: checked,
                // Set published_at when publishing for the first time
                published_at: checked && !prev.isPublished ? new Date().toISOString() : prev.published_at
              }))}
            />
            <Label htmlFor="published">
              {formData.isPublished ? 'Published' : 'Draft'}
            </Label>
          </div>
          <div className="space-x-2">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit">
              {wod ? 'Update WOD' : 'Create WOD'}
            </Button>
          </div>
        </div>

        {/* Rest of the component remains the same */}
        <div className="grid grid-cols-3 gap-8">
          {/* Left Column: Date and Warmup */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="date">Date</Label>
              <Input
                type="date"
                id="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="warmup">Warm-up</Label>
              <textarea
                id="warmup"
                value={formData.warmup}
                onChange={(e) => setFormData(prev => ({ ...prev, warmup: e.target.value }))}
                className="w-full h-[calc(100vh-300px)] p-2 border rounded-md"
                placeholder="Enter warm-up in Markdown format..."
              />
            </div>
          </div>

          {/* Middle Column: WOD Content */}
          <div>
            <Label htmlFor="content">WOD Content</Label>
            <textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              className="w-full h-[calc(100vh-300px)] p-2 border rounded-md"
              placeholder="Enter WOD content in Markdown format..."
              required
            />
          </div>

          {/* Right Column: Exercises */}
          <div className="space-y-4">
            <div>
              <Label>Exercises</Label>
              <Input
                type="text"
                placeholder="Search exercises..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-2"
              />
              
              {/* Alphabet Navigation */}
              <div className="flex flex-wrap gap-1 mb-2">
                <Button 
                  type="button" // Important: specify button type
                  variant={alphabetFilter === null ? "default" : "outline"} 
                  size="sm"
                  onClick={(e) => handleAlphabetClick(e, null)}
                  className="text-xs"
                >
                  All
                </Button>
                {Array.from('ABCDEFGHIJKLMNOPQRSTUVWXYZ').map(letter => (
                  <Button
                    key={letter}
                    type="button" // Important: specify button type
                    variant={alphabetFilter === letter ? "default" : "outline"}
                    size="sm"
                    className="text-xs"
                    onClick={(e) => handleAlphabetClick(e, letter)}
                  >
                    {letter}
                  </Button>
                ))}
              </div>
              
              {/* Results count */}
              <div className="text-sm text-muted-foreground mb-2">
                {filteredExercises.length} exercises found
              </div>
              
              {/* Selected Exercises */}
              <div className="mb-4">
                <div className="text-sm font-medium mb-2">Selected Exercises:</div>
                <div className="flex flex-wrap gap-2">
                  {formData.exercises.map(exerciseId => {
                    const exercise = exercises.find(ex => ex.id === exerciseId);
                    return exercise ? (
                      <Badge 
                        key={exercise.id}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {exercise.exercise_name}
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({
                            ...prev,
                            exercises: prev.exercises.filter(id => id !== exercise.id)
                          }))}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ) : null;
                  })}
                </div>
              </div>

              {/* Exercise List */}
              <ScrollArea className="h-[calc(100vh-450px)] border rounded-md">
                <div className="p-4 space-y-2">
                  {filteredExercises.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      No exercises found
                    </div>
                  ) : (
                    filteredExercises.map(exercise => (
                      <div
                        key={exercise.id}
                        className={`p-2 rounded cursor-pointer transition-colors ${
                          formData.exercises.includes(exercise.id)
                            ? 'bg-primary/10 hover:bg-primary/20'
                            : 'hover:bg-accent'
                        }`}
                        onClick={(e) => {
                          e.preventDefault(); // Prevent form submission
                          setFormData(prev => ({
                            ...prev,
                            exercises: prev.exercises.includes(exercise.id)
                              ? prev.exercises.filter(id => id !== exercise.id)
                              : [...prev.exercises, exercise.id]
                          }));
                        }}
                      >
                        {exercise.exercise_name}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
