// hooks/useSupabase.ts
import { useState, useCallback, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';
import type { User } from '@supabase/supabase-js';

type Payment = Database['public']['Tables']['pliromes']['Insert'];

export function useSupabase() {
  const [user, setUser] = useState<User | null>(null);
  const supabase = createClientComponentClient<Database>();

  // Fetch current user on hook initialization
  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    fetchUser();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Payments
  const fetchPayments = useCallback(async () => {
    const { data, error } = await supabase
      .from('pliromes')
      .select(`
        *,
        pelates (
          name,
          last_name
        )
      `)
      .order('start_program', { ascending: false });

    if (error) throw error;
    return data;
  }, []);

  const addPayment = useCallback(async (paymentData: Payment) => {
    const { data, error } = await supabase
      .from('pliromes')
      .insert(paymentData);

    if (error) throw error;
    return data;
  }, []);

  const updatePayment = useCallback(async (id: string, paymentData: Partial<Payment>) => {
    const { data, error } = await supabase
      .from('pliromes')
      .update(paymentData)
      .eq('id', id);

    if (error) throw error;
    return data;
  }, []);

  const deletePayment = useCallback(async (id: string) => {
    const { error } = await supabase
      .from('pliromes')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }, []);

  // Active Subscriptions (using the view from your schema)
  const fetchActiveSubscriptions = useCallback(async () => {
    const { data, error } = await supabase
      .from('active_subscriptions')
      .select('*');

    if (error) throw error;
    return data;
  }, []);

  // User Authentication
  const signIn = useCallback(async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;
    setUser(data.user);
    return data;
  }, []);

  const signOut = useCallback(async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    setUser(null);
  }, []);

  const getCurrentUser = useCallback(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
    return user;
  }, []);

  return {
    supabase,
    user,
    fetchPayments,
    addPayment,
    updatePayment,
    deletePayment,
    fetchActiveSubscriptions,
    signIn,
    signOut,
    getCurrentUser,
  };
}