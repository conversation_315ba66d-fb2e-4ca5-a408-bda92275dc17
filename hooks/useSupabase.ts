// hooks/useSupabase.ts - COMPLETELY UPDATED VERSION
import { useCallback } from 'react';
import { supabaseClient } from '@/lib/supabase-client'; // ✅ Use singleton instead of creating new clients
import { useAuth } from '@/contexts/AuthContext'; // ✅ Get auth from context instead of managing here
import type { Database } from '@/types/supabase';

type Payment = Database['public']['Tables']['pliromes']['Insert'];

export function useSupabase() {
  const { user, signOut: contextSignOut } = useAuth(); // ✅ Get from context instead of local state

  // ❌ REMOVED: const [user, setUser] = useState<User | null>(null);
  // ❌ REMOVED: const supabase = createClientComponentClient<Database>();
  // ❌ REMOVED: entire useEffect with auth listener - this was creating 221 listeners!

  // ✅ All payment methods updated to use singleton client
  const fetchPayments = useCallback(async () => {
    const { data, error } = await supabaseClient
      .from('pliromes')
      .select(`
        *,
        pelates (
          name,
          last_name
        )
      `)
      .order('start_program', { ascending: false });

    if (error) throw error;
    return data;
  }, []);

  const addPayment = useCallback(async (paymentData: Payment) => {
    const { data, error } = await supabaseClient
      .from('pliromes')
      .insert(paymentData);

    if (error) throw error;
    return data;
  }, []);

  const updatePayment = useCallback(async (id: string, paymentData: Partial<Payment>) => {
    const { data, error } = await supabaseClient
      .from('pliromes')
      .update(paymentData)
      .eq('id', id);

    if (error) throw error;
    return data;
  }, []);

  const deletePayment = useCallback(async (id: string) => {
    const { error } = await supabaseClient
      .from('pliromes')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }, []);

  // Active Subscriptions (using the view from your schema)
  const fetchActiveSubscriptions = useCallback(async () => {
    const { data, error } = await supabaseClient
      .from('active_subscriptions')
      .select('*');

    if (error) throw error;
    return data;
  }, []);

  // ✅ Simplified User Authentication - most auth logic moved to context
  const signIn = useCallback(async (email: string, password: string) => {
    const { data, error } = await supabaseClient.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;
    // ❌ REMOVED: setUser(data.user); - context handles this now
    return data;
  }, []);

  const signOut = useCallback(async () => {
    await contextSignOut(); // ✅ Use context signOut method
  }, [contextSignOut]);

  const getCurrentUser = useCallback(async () => {
    const { data: { user } } = await supabaseClient.auth.getUser();
    // ❌ REMOVED: setUser(user); - context handles this now
    return user;
  }, []);

  return {
    supabaseClient, // ✅ Export singleton for direct queries
    user, // ✅ Now from context instead of local state
    fetchPayments,
    addPayment,
    updatePayment,
    deletePayment,
    fetchActiveSubscriptions,
    signIn,
    signOut,
    getCurrentUser,
  };
}