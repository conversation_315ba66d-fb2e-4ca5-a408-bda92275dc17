// lib/mydata/pdfGenerator.ts
import PDFDocument from 'pdfkit';
import { Buffer } from 'buffer';
import QRCode from 'qrcode';

// Extended PDFKit document type with internal properties
interface ExtendedPDFDocument extends PDFKit.PDFDocument {
  _pageNumber: number;
  _pageCount: number;
}

// Define types for invoice data
interface InvoiceData {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_type: string;
  currency: string;
  total_net: number;
  total_vat: number;
  total_gross: number;
  mark?: string | null;
  qr_url?: string | null;
  created_at: string;
  updated_at: string;
  status: string;
}

interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  net_value: number;
  vat_category: number;
  vat_amount: number;
  income_classification_type: string;
  income_classification_category: string;
}

interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: number;
  amount: number;
  payment_info?: string | null;
}

interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

/**
 * Generates a PDF invoice document
 * @param invoice - Invoice data
 * @param lines - Invoice line items
 * @param paymentMethods - Payment methods
 * @param companySettings - Company information
 * @returns PDF document as Buffer
 */
export async function generateInvoicePdf(
  invoice: InvoiceData,
  lines: InvoiceLine[],
  paymentMethods: PaymentMethod[],
  companySettings: CompanySettings
): Promise<Buffer> {
  // Create a new PDF document with font configuration
  const doc = new PDFDocument({
    size: 'A4',
    margin: 50,
    info: {
      Title: `Invoice ${invoice.invoice_series}-${invoice.invoice_number}`,
      Author: companySettings.companyName,
      Subject: 'Tax Invoice',
      Keywords: 'invoice, myDATA, AADE',
      CreationDate: new Date(),
    },
    // Use default font configuration
    font: undefined
  });

  // Set up buffer to collect PDF data
  const chunks: Buffer[] = [];
  doc.on('data', (chunk) => chunks.push(chunk));

  // Generate PDF content
  await createInvoiceContent(doc, invoice, lines, paymentMethods, companySettings);

  // Finalize the PDF document
  doc.end();

  // Return compiled PDF as Buffer when complete
  return new Promise<Buffer>((resolve) => {
    doc.on('end', () => {
      // Use a type assertion to fix the TypeScript error
      const pdfBuffer = Buffer.concat(chunks as Uint8Array[]);
      resolve(pdfBuffer);
    });
  });
}

/**
 * Creates the content for the invoice PDF
 */
async function createInvoiceContent(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData,
  lines: InvoiceLine[],
  paymentMethods: PaymentMethod[],
  companySettings: CompanySettings
): Promise<void> {
  // Set up fonts
  setupFonts();

  // Add invoice header
  addHeader(doc, invoice, companySettings);

  // Add company and client information
  addCompanyAndClientInfo(doc, invoice, companySettings);

  // Add invoice details
  addInvoiceDetails(doc, invoice);

  // Add line items
  addLineItems(doc, lines, invoice.currency);

  // Add payment methods
  addPaymentMethods(doc, paymentMethods, invoice.currency);

  // Add invoice totals
  addTotals(doc, invoice, 275);

  // Add myDATA information if available
  if (invoice.mark) {
    await addMyDataInfo(doc, invoice);
  }

  // Add footer
  addFooter(doc, companySettings);
}

/**
 * Sets up fonts for the PDF document
 */
function setupFonts(/* doc: PDFKit.PDFDocument */): void {
  // In a server environment like Next.js, we need to be careful with font handling
  // We're intentionally not setting any fonts to avoid file system access issues
  // PDFKit will use its default font
}

/**
 * Adds the invoice header section
 */
function addHeader(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData,
  companySettings: CompanySettings
): void {
  // Add company name
  doc.fontSize(18)
     .fillColor('#333333')
     .text(companySettings.companyName, 50, 50, { width: 300 });

  // Add invoice title
  doc.fontSize(20)
     .fillColor('#333333')
     .text('INVOICE', 400, 50, { align: 'right' });

  // Add invoice number
  doc.fontSize(10)
     .fillColor('#555555')
     .text(`Invoice Number: ${invoice.invoice_series}-${invoice.invoice_number}`, 400, 80, { align: 'right' });

  // Add issue date
  const formattedDate = new Date(invoice.issue_date).toLocaleDateString('el-GR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  doc.text(`Issue Date: ${formattedDate}`, 400, 95, { align: 'right' });

  // Add invoice type
  const invoiceTypes: Record<string, string> = {
    '1.1': 'Sales Invoice',
    '2.1': 'Credit Note',
    '11.1': 'Retail Receipt',
    '11.2': 'Simplified Invoice'
  };

  const invoiceTypeName = invoiceTypes[invoice.invoice_type] || invoice.invoice_type;
  doc.text(`Type: ${invoiceTypeName}`, 400, 110, { align: 'right' });

  // Draw a line under the header
  doc.moveTo(50, 130)
     .lineTo(550, 130)
     .stroke('#dddddd');
}

/**
 * Adds company and client information
 */
function addCompanyAndClientInfo(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData,
  companySettings: CompanySettings
): void {
  // Company information - Left side
  doc.fontSize(10)
     .fillColor('#333333')
     .text('From:', 50, 150);

  doc.fontSize(9)
     .fillColor('#555555')
     .text(companySettings.companyName, 50, 170)
     .text(`VAT: ${companySettings.vatNumber}`, 50, 185)
     .text(`Country: ${companySettings.country}`, 50, 200);

  if (companySettings.address) {
    doc.text(`Address: ${companySettings.address}`, 50, 215);
  }

  if (companySettings.postalCode && companySettings.city) {
    doc.text(`${companySettings.postalCode}, ${companySettings.city}`, 50, 230);
  }

  // Client information - Right side
  doc.fontSize(10)
     .fillColor('#333333')
     .text('To:', 300, 150);

  doc.fontSize(9)
     .fillColor('#555555')
     .text(invoice.client_name, 300, 170)
     .text(`VAT: ${invoice.client_vat}`, 300, 185)
     .text(`Country: ${invoice.client_country}`, 300, 200);

  // Draw a line under the company and client info
  doc.moveTo(50, 250)
     .lineTo(550, 250)
     .stroke('#dddddd');
}

/**
 * Adds general invoice details
 */
function addInvoiceDetails(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData
): void {
  // Add MARK if available (not null or undefined)
  if (invoice.mark !== null && invoice.mark !== undefined) {
    doc.fontSize(9)
       .fillColor('#555555')
       .text(`myDATA MARK: ${invoice.mark}`, 50, 270);
  }

  // Add status indicator with color
  const statusColors: Record<string, string> = {
    draft: '#888888',
    submitted: '#009933',
    error: '#cc0000'
  };

  const statusColor = statusColors[invoice.status] || '#888888';

  doc.fontSize(9)
     .fillColor(statusColor)
     .text(`Status: ${invoice.status.toUpperCase()}`, 450, 270);
}

/**
 * Adds line items table
 * @returns The y position for the next section
 */
function addLineItems(
  doc: PDFKit.PDFDocument,
  lines: InvoiceLine[],
  currency: string
): number {
  // Table headers
  const tableTop = 300;

  doc.fontSize(9)
     .fillColor('#333333')
     .text('#', 50, tableTop)
     .text('Description', 80, tableTop)
     .text('Qty', 280, tableTop, { width: 40, align: 'right' })
     .text('Unit Price', 330, tableTop, { width: 70, align: 'right' })
     .text('VAT', 410, tableTop, { width: 40, align: 'right' })
     .text('Amount', 470, tableTop, { width: 70, align: 'right' });

  // Header underline
  doc.moveTo(50, tableTop + 15)
     .lineTo(550, tableTop + 15)
     .stroke('#dddddd');

  // Map VAT category codes to percentages
  const vatRates: Record<number, string> = {
    1: '24%',
    2: '13%',
    3: '6%',
    4: '17%',
    5: '9%',
    6: '4%'
  };

  // Table rows
  let y = tableTop + 25;

  lines.forEach((line) => {
    // Check if we need a new page
    if (y > 700) {
      doc.addPage();

      // Reset y position for the new page
      y = 50;

      // Add table headers on new page
      doc.fontSize(9)
         .fillColor('#333333')
         .text('#', 50, y)
         .text('Description', 80, y)
         .text('Qty', 280, y, { width: 40, align: 'right' })
         .text('Unit Price', 330, y, { width: 70, align: 'right' })
         .text('VAT', 410, y, { width: 40, align: 'right' })
         .text('Amount', 470, y, { width: 70, align: 'right' });

      // Header underline
      doc.moveTo(50, y + 15)
         .lineTo(550, y + 15)
         .stroke('#dddddd');

      // Update y for the first row on new page
      y += 25;
    }

    // Add line item
    doc.fontSize(9)
       .fillColor('#555555')
       .text(line.line_number.toString(), 50, y)
       .text(line.description, 80, y, { width: 190 })
       .text(line.quantity.toString(), 280, y, { width: 40, align: 'right' })
       .text(formatCurrency(line.unit_price, currency), 330, y, { width: 70, align: 'right' })
       .text(vatRates[line.vat_category] || `${line.vat_category}%`, 410, y, { width: 40, align: 'right' })
       .text(formatCurrency(line.net_value + line.vat_amount, currency), 470, y, { width: 70, align: 'right' });

    // Update y for next row, with variable height based on description
    const lineHeight = Math.max(doc.heightOfString(line.description, { width: 190 }), 15);
    y += lineHeight + 10;
  });

  // Draw a line under the line items
  doc.moveTo(50, y)
     .lineTo(550, y)
     .stroke('#dddddd');

  // Return the y position for the next section
  return y + 10;
}

/**
 * Adds payment methods information
 */
function addPaymentMethods(
  doc: PDFKit.PDFDocument,
  paymentMethods: PaymentMethod[],
  currency: string
): void {
  // Only add payment methods if there are any
  if (!paymentMethods || paymentMethods.length === 0) {
    return;
  }

  // Get current y position
  const { y } = doc;
  const paymentMethodsY = y + 20;

  // Payment methods header
  doc.fontSize(9)
     .fillColor('#333333')
     .text('Payment Methods:', 50, paymentMethodsY);

  // Map payment type codes to names
  const paymentTypes: Record<number, string> = {
    1: 'Domestic Payment',
    2: 'International Payment',
    3: 'Bank Payment',
    4: 'Cash Payment',
    5: 'Check Payment',
    7: 'POS Payment'
  };

  // Payment methods list
  let pmY = paymentMethodsY + 20;

  paymentMethods.forEach((method) => {
    const paymentTypeName = paymentTypes[method.payment_type] || `Type ${method.payment_type}`;

    doc.fontSize(9)
       .fillColor('#555555')
       .text(`• ${paymentTypeName}: ${formatCurrency(method.amount, currency)}`, 70, pmY);

    // Add payment info if available (not null or undefined)
    if (method.payment_info !== null && method.payment_info !== undefined) {
      doc.fontSize(8)
         .fillColor('#777777')
         .text(method.payment_info, 90, pmY + 15);

      pmY += 30;
    } else {
      pmY += 15;
    }
  });
}

/**
 * Adds totals section
 */
function addTotals(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData,
  y: number
): void {
  const totalsY = Math.max(y, 650);

  // Draw a box for totals
  doc.rect(350, totalsY, 200, 80)
     .fillAndStroke('#f9f9f9', '#dddddd');

  // Add totals
  doc.fontSize(9)
     .fillColor('#555555')
     .text('Net Amount:', 370, totalsY + 15)
     .text(formatCurrency(invoice.total_net, invoice.currency), 480, totalsY + 15, { align: 'right', width: 60 });

  doc.text('VAT Amount:', 370, totalsY + 35)
     .text(formatCurrency(invoice.total_vat, invoice.currency), 480, totalsY + 35, { align: 'right', width: 60 });

  doc.fontSize(11)
     .fillColor('#333333')
     .text('Total:', 370, totalsY + 55)
     .text(formatCurrency(invoice.total_gross, invoice.currency), 480, totalsY + 55, { align: 'right', width: 60 });
}

/**
 * Adds myDATA information including QR code
 */
async function addMyDataInfo(
  doc: PDFKit.PDFDocument,
  invoice: InvoiceData
): Promise<void> {
  // Add only if the invoice has a MARK (not null or undefined)
  if (invoice.mark === null || invoice.mark === undefined) {
    return;
  }

  // Create myDATA info box
  const boxY = 650;

  doc.rect(50, boxY, 280, 80)
     .fillAndStroke('#f9f9f9', '#dddddd');

  // Add myDATA header
  doc.fontSize(10)
     .fillColor('#333333')
     .text('myDATA Electronic Invoice Verification', 60, boxY + 10);

  // Add MARK
  doc.fontSize(9)
     .fillColor('#555555')
     .text(`MARK: ${invoice.mark}`, 60, boxY + 30);

  // Add QR code if available (not null or undefined)
  if (invoice.qr_url !== null && invoice.qr_url !== undefined) {
    try {
      // Generate QR code as data URL
      const qrDataUrl = await QRCode.toDataURL(invoice.qr_url, {
        width: 60,
        margin: 0
      });

      // Extract base64 data from data URL
      const base64Data = qrDataUrl.split(',')[1];

      // Add QR code image
      doc.image(Buffer.from(base64Data, 'base64'), 230, boxY + 10, { width: 60 });

      // Add verification text
      doc.fontSize(7)
         .fillColor('#777777')
         .text('Scan to verify invoice authenticity', 60, boxY + 50, { width: 160 });
    } catch (error) {
      console.error('Error generating QR code:', error);

      // Add verification URL instead
      doc.fontSize(8)
         .fillColor('#0066cc')
         .text('Verification URL:', 60, boxY + 50)
         .text(invoice.qr_url, 60, boxY + 60, { width: 260 });
    }
  }
}

/**
 * Adds footer to the invoice
 */
function addFooter(
  doc: PDFKit.PDFDocument,
  companySettings: CompanySettings
): void {
  const pageHeight = doc.page.height;

  // Draw footer line
  doc.moveTo(50, pageHeight - 60)
     .lineTo(550, pageHeight - 60)
     .stroke('#dddddd');

  // Add footer text
  doc.fontSize(8)
     .fillColor('#888888')
     .text(
       `This is an electronic invoice issued by ${companySettings.companyName} (VAT: ${companySettings.vatNumber}).` +
       ' This document is valid for tax purposes and has been transmitted to AADE myDATA system.',
       50,
       pageHeight - 50,
       { width: 500, align: 'center' }
     );

  // Add page number
  // Use ExtendedPDFDocument type for accessing internal properties
  const pdfDoc = doc as ExtendedPDFDocument;
  doc.fontSize(8)
     .fillColor('#888888')
     .text(
       `Page ${pdfDoc._pageNumber} of ${pdfDoc._pageCount}`,
       50,
       pageHeight - 25,
       { width: 500, align: 'center' }
     );
}

/**
 * Formats currency values
 */
function formatCurrency(amount: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('el-GR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount);
}