// lib/mydata/reactPdfGenerator.tsx
import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf
} from '@react-pdf/renderer';

// Define types for invoice data
interface InvoiceData {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_type: string;
  currency: string;
  total_net: number;
  total_vat: number;
  total_gross: number;
  mark?: string | null;
  qr_url?: string | null;
  created_at: string;
  updated_at: string;
  status: string;
}

interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  net_value: number;
  vat_category: number;
  vat_amount: number;
  income_classification_type: string;
  income_classification_category: string;
}

interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: number;
  amount: number;
  payment_info?: string | null;
}

interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'column',
    maxWidth: '60%',
  },
  headerRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 12,
    marginBottom: 5,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#DDDDDD',
    marginVertical: 10,
  },
  infoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  infoColumn: {
    flexDirection: 'column',
    width: '48%',
  },
  infoTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 9,
    marginBottom: 3,
  },
  table: {
    marginTop: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#DDDDDD',
    paddingBottom: 5,
    backgroundColor: '#F5F5F5',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingVertical: 5,
  },
  tableCol1: {
    width: '5%',
    fontSize: 9,
  },
  tableCol2: {
    width: '40%',
    fontSize: 9,
  },
  tableCol3: {
    width: '10%',
    fontSize: 9,
    textAlign: 'right',
  },
  tableCol4: {
    width: '15%',
    fontSize: 9,
    textAlign: 'right',
  },
  tableCol5: {
    width: '10%',
    fontSize: 9,
    textAlign: 'right',
  },
  tableCol6: {
    width: '20%',
    fontSize: 9,
    textAlign: 'right',
  },
  totalsSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  totalsTable: {
    width: '40%',
  },
  totalsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 3,
  },
  totalsLabel: {
    fontSize: 9,
  },
  totalsValue: {
    fontSize: 9,
    textAlign: 'right',
  },
  totalFinal: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 8,
    color: '#666666',
  },
  myDataSection: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#F9F9F9',
    borderWidth: 1,
    borderColor: '#DDDDDD',
    width: '60%',
  },
  myDataTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  myDataText: {
    fontSize: 9,
    marginBottom: 3,
  },
});

// Format currency
const formatCurrency = (amount: number, currency: string = 'EUR'): string => {
  return new Intl.NumberFormat('el-GR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount);
};

// Create Invoice Document Component
const InvoiceDocument = ({
  invoice,
  lines,
  companySettings
}: {
  invoice: InvoiceData;
  lines: InvoiceLine[];
  companySettings: CompanySettings;
}) => {
  // Map VAT category codes to percentages
  const vatRates: Record<number, string> = {
    1: '24%',
    2: '13%',
    3: '6%',
    4: '17%',
    5: '9%',
    6: '4%'
  };

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <Document
      title={`Invoice ${invoice.invoice_series}-${invoice.invoice_number}`}
      author={companySettings.companyName}
      subject="Tax Invoice"
      keywords="invoice, myDATA, AADE"
    >
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.title}>{companySettings.companyName}</Text>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.title}>INVOICE</Text>
            <Text style={styles.subtitle}>
              Invoice Number: {invoice.invoice_series}-{invoice.invoice_number}
            </Text>
            <Text style={styles.subtitle}>
              Issue Date: {formatDate(invoice.issue_date)}
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        {/* Company and Client Information */}
        <View style={styles.infoSection}>
          <View style={styles.infoColumn}>
            <Text style={styles.infoTitle}>From:</Text>
            <Text style={styles.infoText}>{companySettings.companyName}</Text>
            <Text style={styles.infoText}>VAT: {companySettings.vatNumber}</Text>
            <Text style={styles.infoText}>Country: {companySettings.country}</Text>
            {companySettings.address && (
              <Text style={styles.infoText}>Address: {companySettings.address}</Text>
            )}
            {companySettings.postalCode && companySettings.city && (
              <Text style={styles.infoText}>
                {companySettings.postalCode}, {companySettings.city}
              </Text>
            )}
          </View>
          <View style={styles.infoColumn}>
            <Text style={styles.infoTitle}>To:</Text>
            <Text style={styles.infoText}>{invoice.client_name}</Text>
            <Text style={styles.infoText}>VAT: {invoice.client_vat}</Text>
            <Text style={styles.infoText}>Country: {invoice.client_country}</Text>
          </View>
        </View>

        <View style={styles.divider} />

        {/* Invoice Details */}
        {invoice.mark && (
          <Text style={styles.infoText}>myDATA MARK: {invoice.mark}</Text>
        )}

        {/* Line Items Table */}
        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={styles.tableCol1}>#</Text>
            <Text style={styles.tableCol2}>Description</Text>
            <Text style={styles.tableCol3}>Qty</Text>
            <Text style={styles.tableCol4}>Unit Price</Text>
            <Text style={styles.tableCol5}>VAT</Text>
            <Text style={styles.tableCol6}>Amount</Text>
          </View>

          {lines.map((line) => (
            <View style={styles.tableRow} key={line.id}>
              <Text style={styles.tableCol1}>{line.line_number}</Text>
              <Text style={styles.tableCol2}>{line.description}</Text>
              <Text style={styles.tableCol3}>{line.quantity}</Text>
              <Text style={styles.tableCol4}>
                {formatCurrency(line.unit_price, invoice.currency)}
              </Text>
              <Text style={styles.tableCol5}>
                {vatRates[line.vat_category] || `${line.vat_category}%`}
              </Text>
              <Text style={styles.tableCol6}>
                {formatCurrency(line.net_value + line.vat_amount, invoice.currency)}
              </Text>
            </View>
          ))}
        </View>

        {/* Totals Section */}
        <View style={styles.totalsSection}>
          <View style={styles.totalsTable}>
            <View style={styles.totalsRow}>
              <Text style={styles.totalsLabel}>Net Total:</Text>
              <Text style={styles.totalsValue}>
                {formatCurrency(invoice.total_net, invoice.currency)}
              </Text>
            </View>
            <View style={styles.totalsRow}>
              <Text style={styles.totalsLabel}>VAT Total:</Text>
              <Text style={styles.totalsValue}>
                {formatCurrency(invoice.total_vat, invoice.currency)}
              </Text>
            </View>
            <View style={styles.totalsRow}>
              <Text style={[styles.totalsLabel, styles.totalFinal]}>Grand Total:</Text>
              <Text style={[styles.totalsValue, styles.totalFinal]}>
                {formatCurrency(invoice.total_gross, invoice.currency)}
              </Text>
            </View>
          </View>
        </View>

        {/* Footer */}
        <Text style={styles.footer}>
          This is an electronic invoice issued by {companySettings.companyName} (VAT: {companySettings.vatNumber}).
          This document is valid for tax purposes and has been transmitted to AADE myDATA system.
        </Text>
      </Page>
    </Document>
  );
};

// Generate PDF buffer
export async function generateReactInvoicePdf(
  invoice: InvoiceData,
  lines: InvoiceLine[],
  paymentMethods: PaymentMethod[],
  companySettings: CompanySettings
): Promise<Buffer> {
  try {
    console.log('Generating PDF for invoice:', invoice.invoice_series, invoice.invoice_number);
    console.log('Number of invoice lines:', lines.length);

    // Validate required data
    if (!invoice || !lines || lines.length === 0 || !companySettings) {
      console.error('Missing required data for PDF generation:', {
        hasInvoice: !!invoice,
        hasLines: !!lines,
        lineCount: lines?.length || 0,
        hasCompanySettings: !!companySettings
      });
      throw new Error('Missing required data for PDF generation');
    }

    // Generate PDF
    const pdfBuffer = await pdf(
      <InvoiceDocument
        invoice={invoice}
        lines={lines}
        companySettings={companySettings}
      />
    ).toBuffer();

    // First cast to unknown, then to Buffer to satisfy TypeScript
    const buffer = pdfBuffer as unknown as Buffer;
    console.log('PDF generated successfully, buffer size:', buffer.length);
    return buffer;
  } catch (error) {
    console.error('Error in PDF generation:', error);
    throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
