// lib/settings.ts
import { createClient } from '@supabase/supabase-js';

    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
/**
 * Retrieves company settings from the database
 * @returns Company settings object or null if not found
 */
export async function getCompanySettings() {
  try {
    const { data, error } = await supabase
      .from('company_settings')
      .select('*')
      .single();

      console.log('Settings fetch result:', { data, error });
    
    if (error) {
      console.error('Error fetching company settings:', error);
      return null;
    }
    
    // Ensure we return with the proper field names
    return {
      vatNumber: data.vat_number,
      country: data.country || 'GR',
      branch: data.branch || '0',
      defaultClassificationType: data.default_classification_type,
      defaultClassificationCategory: data.default_classification_category
    };
  } catch (error) {
    console.error('Exception in getCompanySettings:', error);
    return null;
  }
}

/**
 * Saves company settings to the database
 * @param settings Company settings to save
 * @returns Success status
 */
export async function saveCompanySettings(settings: {
  vatNumber: string;
  country?: string;
  branch?: string;
  defaultClassificationType?: string;
  defaultClassificationCategory?: string;
}) {
  try {
    // Convert to snake_case for database
    const dbSettings = {
      vat_number: settings.vatNumber,
      country: settings.country || 'GR',
      branch: settings.branch || '0',
      default_classification_type: settings.defaultClassificationType,
      default_classification_category: settings.defaultClassificationCategory
    };
    
    // Check if settings already exist
    const { data, error: fetchError } = await supabase
      .from('company_settings')
      .select('id')
      .limit(1);
    
    if (fetchError) {
      console.error('Error checking company settings:', fetchError);
      return false;
    }
    
    if (data && data.length > 0) {
      // Update existing settings
      const { error } = await supabase
        .from('company_settings')
        .update(dbSettings)
        .eq('id', data[0].id);
      
      if (error) {
        console.error('Error updating company settings:', error);
        return false;
      }
    } else {
      // Insert new settings
      const { error } = await supabase
        .from('company_settings')
        .insert(dbSettings);
      
      if (error) {
        console.error('Error inserting company settings:', error);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Exception in saveCompanySettings:', error);
    return false;
  }
}