// lib/mydata/setupDatabase.ts
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

export async function setupMyDataTables() {
  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  // Create company_settings table if it doesn't exist
  const { error: createCompanyError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.company_settings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        companyName TEXT NOT NULL,
        vatNumber TEXT NOT NULL,
        country TEXT NOT NULL DEFAULT 'GR',
        branch TEXT NOT NULL DEFAULT '0',
        address TEXT,
        postalCode TEXT,
        city TEXT,
        defaultClassificationType TEXT DEFAULT 'E3_561_007',
        defaultClassificationCategory TEXT DEFAULT 'category1_3',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (createCompanyError) {
    console.error('Failed to create company_settings table:', createCompanyError);
  }

  // Create environment_variables table if it doesn't exist
  const { error: createEnvError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.environment_variables (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        description TEXT,
        is_secret BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (createEnvError) {
    console.error('Failed to create environment_variables table:', createEnvError);
  }

  // Create invoices table if it doesn't exist
  const { error: createInvoicesError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.invoices (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        client_id UUID REFERENCES public.pelates(id),
        client_name TEXT,
        client_vat TEXT,
        client_country TEXT DEFAULT 'GR',
        invoice_series TEXT NOT NULL,
        invoice_number TEXT NOT NULL,
        issue_date DATE NOT NULL,
        invoice_type TEXT NOT NULL DEFAULT '11.2',
        total_net DECIMAL(10, 2) NOT NULL,
        total_vat DECIMAL(10, 2) NOT NULL,
        total_gross DECIMAL(10, 2) NOT NULL,
        currency TEXT DEFAULT 'EUR',
        status TEXT NOT NULL DEFAULT 'draft',
        mark TEXT,
        qr_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(invoice_series, invoice_number)
      );
    `
  });

  if (createInvoicesError) {
    console.error('Failed to create invoices table:', createInvoicesError);
  }

  // Create invoice_lines table if it doesn't exist
  const { error: createLinesError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.invoice_lines (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
        line_number INTEGER NOT NULL,
        description TEXT NOT NULL,
        quantity DECIMAL(10, 2) NOT NULL,
        unit_price DECIMAL(10, 2) NOT NULL,
        net_value DECIMAL(10, 2) NOT NULL,
        vat_category INTEGER NOT NULL,
        vat_amount DECIMAL(10, 2) NOT NULL,
        income_classification_type TEXT NOT NULL,
        income_classification_category TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (createLinesError) {
    console.error('Failed to create invoice_lines table:', createLinesError);
  }

  // Create invoice_payment_methods table if it doesn't exist
  const { error: createPaymentsError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.invoice_payment_methods (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
        payment_type INTEGER NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        payment_info TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (createPaymentsError) {
    console.error('Failed to create invoice_payment_methods table:', createPaymentsError);
  }

  // Create api_logs table if it doesn't exist
  const { error: createLogsError } = await supabase.rpc('execute_sql', {
    sql_query: `
      CREATE TABLE IF NOT EXISTS public.api_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        invoice_id UUID REFERENCES public.invoices(id),
        request_type TEXT NOT NULL,
        request_body TEXT NOT NULL,
        response_body TEXT,
        status TEXT NOT NULL,
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (createLogsError) {
    console.error('Failed to create api_logs table:', createLogsError);
  }

  return {
    success: !createCompanyError && !createEnvError && !createInvoicesError &&
             !createLinesError && !createPaymentsError && !createLogsError
  };
}