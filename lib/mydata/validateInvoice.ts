// lib/mydata/validateInvoice.ts
import { Invoice, InvoiceLine, PaymentMethod } from '@/types/mydata';

/**
 * Validates an invoice, its lines, and payment methods according to myDATA rules
 * @param invoice The invoice to validate
 * @param lines Invoice line items
 * @param paymentMethods Payment methods for the invoice
 * @returns Array of validation error messages (empty if valid)
 */
export function validateInvoiceForMyDATA(
  invoice: Invoice, 
  lines: InvoiceLine[], 
  paymentMethods: PaymentMethod[]
): string[] {
  const errors: string[] = [];
  
  // Required invoice fields
  if (!invoice.invoice_series) {
    errors.push('Invoice series is required');
  }
  
  if (!invoice.invoice_number) {
    errors.push('Invoice number is required');
  }
  
  if (!invoice.issue_date) {
    errors.push('Issue date is required');
  }
  
  if (!invoice.invoice_type) {
    errors.push('Invoice type is required');
  }
  
  // Validate client information
  if (!invoice.client_vat) {
    errors.push('Client VAT number is required');
  } else if (invoice.client_country === 'GR' && !/^\d{9}$/.test(invoice.client_vat)) {
    errors.push('Greek VAT numbers must be 9 digits');
  }
  
  if (!invoice.client_country) {
    errors.push('Client country is required');
  } else if (!/^[A-Z]{2}$/.test(invoice.client_country)) {
    errors.push('Country code must be a 2-character ISO code (e.g., GR)');
  }
  
  // Validate invoice lines exist
  if (!lines || lines.length === 0) {
    errors.push('Invoice must have at least one line item');
  } else {
    // Check each line
    lines.forEach((line, index) => {
      if (!line.description) {
        errors.push(`Line ${index + 1}: Description is required`);
      }
      
      if (!line.quantity || line.quantity <= 0) {
        errors.push(`Line ${index + 1}: Quantity must be greater than zero`);
      }
      
      if (line.net_value === undefined || line.net_value === null || line.net_value < 0) {
        errors.push(`Line ${index + 1}: Net value cannot be negative`);
      }
      
      if (line.vat_amount === undefined || line.vat_amount === null || line.vat_amount < 0) {
        errors.push(`Line ${index + 1}: VAT amount cannot be negative`);
      }
    });
  }
  
  // Validate payment methods
  if (!paymentMethods || paymentMethods.length === 0) {
    errors.push('At least one payment method is required');
  } else {
    // Calculate totals
    const invoiceTotal = calculateTotalGrossValue(lines);
    const paymentsTotal = paymentMethods.reduce((sum, method) => sum + Number(method.amount || 0), 0);
    
    // Check payment methods total matches invoice total (with small rounding tolerance)
    if (Math.abs(invoiceTotal - paymentsTotal) > 0.02) {
      errors.push(
        `Payment methods total (${paymentsTotal.toFixed(2)}) doesn't match invoice total (${invoiceTotal.toFixed(2)})`
      );
    }
    
    // Check each payment method
    paymentMethods.forEach((method, index) => {
      if (!method.amount || method.amount <= 0) {
        errors.push(`Payment method ${index + 1}: Amount must be greater than zero`);
      }
      
      // Validate payment type is allowed
      if (![1, 2, 3, 4, 5, 6, 7].includes(Number(method.payment_type))) {
        errors.push(`Payment method ${index + 1}: Invalid payment type ${method.payment_type}`);
      }
    });
  }
  
  return errors;
}

// Calculate total gross value (net + VAT)
function calculateTotalGrossValue(lines: InvoiceLine[]): number {
  return lines.reduce((sum, line) => 
    sum + Number(line.net_value || 0) + Number(line.vat_amount || 0), 0
  );
}