// lib/mydata/validateXml.ts
/**
 * Validates myDATA invoice XML structure
 * @param xml The XML string to validate
 * @returns Object with validation result and any errors
 */
export function validateMyDataInvoiceXml(xml: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for basic XML syntax errors
    if (!xml || !xml.trim().startsWith('<?xml')) {
      errors.push('Invalid XML: Missing XML declaration');
      return { valid: false, errors };
    }
    
    // Check for required elements
    if (!/<InvoicesDoc[^>]*>/i.test(xml)) {
      errors.push('Missing required root element: <InvoicesDoc>');
    }
    
    if (!/<invoice>/i.test(xml)) {
      errors.push('Missing required element: <invoice>');
    }
    
    if (!/<issuer>/i.test(xml)) {
      errors.push('Missing required element: <issuer>');
    }
    
    if (!/<counterpart>/i.test(xml)) {
      errors.push('Missing required element: <counterpart>');
    }
    
    if (!/<invoiceHeader>/i.test(xml)) {
      errors.push('Missing required element: <invoiceHeader>');
    }
    
    if (!/<invoiceDetails>/i.test(xml)) {
      errors.push('Missing required element: <invoiceDetails>');
    }
    
    if (!/<invoiceSummary>/i.test(xml)) {
      errors.push('Missing required element: <invoiceSummary>');
    }
    
    // Check for common myDATA XML errors
    errors.push(...checkForCommonMyDataErrors(xml));
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Check for common myDATA XML structure issues
   * @param xml XML string to check
   * @returns Array of error messages
   */
  function checkForCommonMyDataErrors(xml: string): string[] {
    const errors: string[] = [];
    
    // Check for <quantity> instead of <quantity15>
    if (/<quantity>[^<]+<\/quantity>/i.test(xml)) {
      errors.push('Invalid element: <quantity> should be <quantity15>');
    }
    
    // Check for <measurementUnit> as direct child of <invoiceDetails>
    const invoiceDetailsRegex = /<invoiceDetails>([\s\S]*?)<\/invoiceDetails>/g;
    let match;
    
    while ((match = invoiceDetailsRegex.exec(xml)) !== null) {
      const detailsContent = match[1];
      if (/<measurementUnit>[^<]+<\/measurementUnit>/i.test(detailsContent)) {
        errors.push('Invalid structure: <measurementUnit> cannot be a direct child of <invoiceDetails>');
      }
    }
    
    // Check for missing required elements in invoiceDetails
    const lineNumberRegex = /<lineNumber>[^<]+<\/lineNumber>/i;
    const netValueRegex = /<netValue>[^<]+<\/netValue>/i;
    const vatCategoryRegex = /<vatCategory>[^<]+<\/vatCategory>/i;
    const vatAmountRegex = /<vatAmount>[^<]+<\/vatAmount>/i;
    
    let detailsMatch;
    const detailsBlocks = [];
    
    // Extract all invoiceDetails blocks
    while ((detailsMatch = invoiceDetailsRegex.exec(xml)) !== null) {
      detailsBlocks.push(detailsMatch[1]);
    }
    
    // Check each invoiceDetails block for required elements
    detailsBlocks.forEach((block, index) => {
      if (!lineNumberRegex.test(block)) {
        errors.push(`Invoice details block ${index + 1}: Missing required element <lineNumber>`);
      }
      
      if (!netValueRegex.test(block)) {
        errors.push(`Invoice details block ${index + 1}: Missing required element <netValue>`);
      }
      
      if (!vatCategoryRegex.test(block)) {
        errors.push(`Invoice details block ${index + 1}: Missing required element <vatCategory>`);
      }
      
      if (!vatAmountRegex.test(block)) {
        errors.push(`Invoice details block ${index + 1}: Missing required element <vatAmount>`);
      }
    });
    
    return errors;
  }