// lib/mydata/xmlParser.ts
import { XMLParser } from 'fast-xml-parser';
import type { MyDataResponse, MyDataErrorDetail } from '@/types/mydata';

export function parseXmlResponse(xmlString: string): MyDataResponse {
  const parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "@_",
    textNodeName: "#text",
    isArray: (name) => {
      // Ensure these elements are always treated as arrays even if there's only one
      const alwaysArrayElements = [
        'errors',
        'error',
        'invoices',
        'invoice',
        'invoiceDetails',
        'invoiceDetail',
        'paymentMethodDetails',
        'paymentMethodDetail',
        'classificationDetails',
        'classificationDetail'
      ];
      return alwaysArrayElements.includes(name);
    }
  });

  try {
    const result = parser.parse(xmlString);

    // Handle different response formats
    if (result.InvoicesDoc) {
      // Parse invoice response
      const response = result.InvoicesDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          invoiceMark: response.invoiceMark,
          qrUrl: response.qrUrl,
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.ResponseDoc) {
      // Parse cancellation response
      const response = result.ResponseDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          cancellationMark: response.cancellationMark
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.ClassificationDoc) {
      // Parse classification response
      const response = result.ClassificationDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success',
          classificationMark: response.classificationMark
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.PaymentMethodsDoc) {
      // Parse payment methods response
      const response = result.PaymentMethodsDoc.response || {};

      if (response.statusCode === 'Success') {
        return {
          statusCode: 'Success'
        };
      } else {
        return {
          statusCode: response.statusCode || 'Error',
          errors: parseErrors(response.errors)
        };
      }
    } else if (result.RequestedDoc) {
      // Parse requested documents response
      return {
        statusCode: 'Success'
      };
    } else {
      // Unknown response format
      return {
        statusCode: 'Error',
        errors: [{
          code: 'UNKNOWN',
          message: 'Unknown response format'
        }]
      };
    }
  } catch (error) {
    console.error('Error parsing XML response:', error);
    return {
      statusCode: 'Error',
      errors: [{
        code: 'PARSE_ERROR',
        message: 'Failed to parse XML response'
      }]
    };
  }
}

function parseErrors(errors: { error?: { code?: string; message?: string } | Array<{ code?: string; message?: string }> }): MyDataErrorDetail[] {
  if (!errors) return [];

  // Handle single error (not in array)
  if (errors.error && !Array.isArray(errors.error)) {
    return [{
      code: errors.error.code || 'UNKNOWN',
      message: errors.error.message || 'Unknown error'
    }];
  }

  // Handle array of errors
  if (Array.isArray(errors.error)) {
    return errors.error.map((error) => ({
      code: error.code || 'UNKNOWN',
      message: error.message || 'Unknown error'
    }));
  }

  return [];
}