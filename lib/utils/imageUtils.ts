/**
 * Utility functions for handling images in the application
 */

/**
 * Constructs a URL for an image stored in Supabase Storage
 *
 * @param imageId - The ID or path of the image in the storage bucket
 * @param bucket - The storage bucket name (default: 'exercises')
 * @returns The full URL to the image
 */
export function getImageUrl(imageId: string, bucket: string = 'exercises'): string {
  if (!imageId) {
    console.debug('getImageUrl: Empty imageId provided');
    return getFallbackImageUrl('No+Image+ID');
  }

  // If the imageId is already a full URL, return it as is
  if (imageId.startsWith('http://') || imageId.startsWith('https://')) {
    return imageId;
  }

  // Use environment variable or fallback to the known Supabase URL
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://eebyxmqdzvutxakzijbu.supabase.co';
  if (!supabaseUrl) {
    console.error('NEXT_PUBLIC_SUPABASE_URL is not defined and fallback URL is missing');
    return getFallbackImageUrl('Config+Error');
  }

  // Handle case where imageId might include the bucket name already
  let finalImageId = imageId;
  let finalBucket = bucket;

  // If imageId contains a slash, it might include a path or bucket
  if (imageId.includes('/')) {
    const parts = imageId.split('/');
    // If it's a path with more than one segment, the first might be the bucket
    if (parts.length > 1) {
      // Check if the first part matches a known bucket pattern
      const possibleBucket = parts[0].toLowerCase();
      if (possibleBucket === 'exercises' || possibleBucket === 'recipes' ||
          possibleBucket === 'profiles' || possibleBucket === 'receipts') {
        finalBucket = parts[0];
        finalImageId = parts.slice(1).join('/');
        console.debug('Extracted bucket from imageId:', { finalBucket, finalImageId });
      }
    }
  }

  const fullUrl = `${supabaseUrl}/storage/v1/object/public/${finalBucket}/${finalImageId}`;
  console.debug('getImageUrl generated:', {
    originalImageId: imageId,
    finalImageId,
    originalBucket: bucket,
    finalBucket,
    fullUrl
  });
  return fullUrl;
}

/**
 * Checks if a string is a valid image ID or URL
 *
 * @param value - The string to check
 * @returns True if the string is a valid image ID or URL
 */
export function isValidImageSource(value: string | null | undefined): boolean {
  if (!value) return false;

  // Check if it's a URL
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return true;
  }

  // Check if it's a valid image ID (non-empty string)
  return value.trim().length > 0;
}

/**
 * Gets a fallback image URL for when an image fails to load
 *
 * @param text - Optional text to display on the placeholder image
 * @returns A URL to a placeholder image
 */
export function getFallbackImageUrl(text: string = 'No+Image'): string {
  return `https://placehold.co/100x100?text=${encodeURIComponent(text)}`;
}
