// types/mydata.ts
export type VatCategory = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

export type InvoiceType = 
  '1.1' | '1.2' | '1.3' | '1.4' | '1.5' | '1.6' | 
  '2.1' | '2.2' | '2.3' | '2.4' | 
  '3.1' | '3.2' | 
  '4' |
  '5.1' | '5.2' | 
  '6.1' | '6.2' | 
  '7.1' | 
  '8.1' | '8.2' | 
  '11.1' | '11.2' | '11.3' | '11.4' | '11.5' |
  '12' |
  '13.1' | '13.2' | '13.3' | '13.4' | '13.30' | '13.31' |
  '14.1' | '14.2' | '14.3' | '14.4' | '14.5' | '14.30' | '14.31' |
  '15.1' |
  '16.1' |
  '17.1' | '17.2' | '17.3' | '17.4' | '17.5' | '17.6';

export type PaymentMethodType = 1 | 2 | 3 | 4 | 5 | 6 | 7;

// Classification types as defined by AADE
export type ClassificationType = 
  'E3_106' | 'E3_205' | 'E3_210' | 'E3_305' | 'E3_310' | 
  'E3_318' | 'E3_561_001' | 'E3_561_002' | 'E3_561_003' | 
  'E3_561_004' | 'E3_561_005' | 'E3_561_006' | 'E3_561_007' | 
  'E3_562' | 'E3_563' | 'E3_564' | 'E3_565' | 'E3_566' | 
  'E3_567' | 'E3_568' | 'E3_570' | 'E3_595' | 'E3_596' | 
  'E3_597' | 'E3_880_001' | 'E3_880_002' | 'E3_880_003' | 
  'E3_880_004' | 'E3_881_001' | 'E3_881_002' | 'E3_881_003' | 
  'E3_881_004';

// Classification categories as defined by AADE
export type ClassificationCategory = 
  'category1_1' | 'category1_2' | 'category1_3' | 'category1_4' | 'category1_5' | 'category1_6' | 'category1_7' | 'category1_8' | 'category1_9' | 'category1_10' | 'category1_95';

export interface Address {
  street: string;
  number: string;
  postal_code: string;
  city: string;
}

export interface Invoice {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string | Date;
  invoice_type: InvoiceType;
  currency?: string;
  client_vat: string;
  client_country?: string;
  client_name?: string;
  client_address?: Address;
  // Add other invoice fields as needed
}

export interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  net_value: number;
  vat_category: VatCategory;
  vat_amount: number;
  income_classification_type?: ClassificationType;
  income_classification_category?: ClassificationCategory;
  // Add other line fields as needed
}

export interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: PaymentMethodType;
  amount: number;
  payment_info?: string;
  // Add other payment method fields as needed
}

export interface MyDataResponse {
  ResponseDoc: {
    response: {
      index: number;
      statusCode: string;
      errors?: {
        error: Array<{
          message: string;
          code: string;
        }> | {
          message: string;
          code: string;
        };
      };
      invoiceUid?: string;
      invoiceMark?: string;
      qrUrl?: string;
    };
  };
}

// Type for error responses
export interface MyDataError {
  message: string;
  code: string;
}